"use client"

import { useEffect, useState } from 'react'

type Department = { id: string; name: string }
type Agent = { id: string; fullName: string | null; email: string }

export default function TicketControls({ ticketId, initial, role }: { ticketId: string, initial: { status: string, priority: string, departmentId?: string, assignedToId?: string, projectId?: string }, role: 'ADMIN' | 'MANAGER' | 'AGENT' | 'CUSTOMER' }) {
  const [status, setStatus] = useState(initial.status)
  const [priority, setPriority] = useState(initial.priority)
  const [departmentId, setDepartmentId] = useState(initial.departmentId || '')
  const [assignedToId, setAssignedToId] = useState(initial.assignedToId || '')
  const [agents, setAgents] = useState<Agent[]>([])
  const [departments, setDepartments] = useState<Department[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  useEffect(() => {
    // Load departments (only needed for admin/manager)
    if (role === 'ADMIN' || role === 'MANAGER') {
      (async () => {
        try {
          const res = await fetch('/api/departments')
          const data = await res.json()
          if (res.ok) {
            setDepartments(data.departments || [])
            if (!initial.departmentId && (data.departments || []).length > 0) {
              setDepartmentId(data.departments[0].id)
            }
          }
        } catch {}
      })()
    }
  }, [role])

  useEffect(() => {
    // Load agents filtered by department + project (only for admin/manager)
    if (role === 'ADMIN' || role === 'MANAGER') {
      (async () => {
        try {
          const params = new URLSearchParams()
          params.set('role', 'AGENT')
          if (departmentId) params.set('departmentId', departmentId)
          if (initial.projectId) params.set('projectId', initial.projectId)
          const res = await fetch(`/api/users?${params.toString()}`)
          const data = await res.json()
          if (res.ok) {
            const list: Agent[] = (data.users || []).map((u: any) => ({ id: u.id, fullName: u.fullName, email: u.email }))
            setAgents(list)
            // If current assignee is not in list, clear
            if (assignedToId && !list.some(a => a.id === assignedToId)) {
              setAssignedToId('')
            }
          }
        } catch {}
      })()
    }
  }, [departmentId, initial.projectId, role])

  const save = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')
    try {
      const payload: any = { status, priority }
      if (role === 'ADMIN' || role === 'MANAGER') {
        payload.departmentId = departmentId
        payload.assignedToId = assignedToId || null
      }
      const res = await fetch(`/api/tickets/${ticketId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      })
      if (!res.ok) {
        const data = await res.json().catch(() => ({}))
        setError(data.error || 'Failed to update ticket')
        return
      }
      window.location.reload()
    } catch (e) {
      setError('Unexpected error')
    } finally {
      setLoading(false)
    }
  }

  return (
    <form onSubmit={save} className="space-y-3">
      {error && <div className="bg-red-50 text-red-700 border border-red-200 p-2 rounded">{error}</div>}
      <div>
        <label className="block text-sm font-medium text-gray-700">Status</label>
        {(() => {
          const all = ['OPEN','IN_PROGRESS','WAITING_FOR_CUSTOMER','WAITING_FOR_AGENT','RESOLVED','CLOSED','CANCELLED']
          const allowed = role === 'AGENT' ? ['IN_PROGRESS','WAITING_FOR_CUSTOMER','RESOLVED'] : all
          return (
            <select value={status} onChange={e=>setStatus(e.target.value)} className="mt-1 w-full border rounded p-2">
              {allowed.map(s => <option key={s} value={s}>{s}</option>)}
            </select>
          )
        })()}
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700">Priority</label>
        {(() => {
          const all = ['LOW','MEDIUM','HIGH','URGENT']
          const allowed = role === 'AGENT' ? ['LOW','MEDIUM','HIGH'] : all
          return (
            <select value={priority} onChange={e=>setPriority(e.target.value)} className="mt-1 w-full border rounded p-2">
              {allowed.map(p => <option key={p} value={p}>{p}</option>)}
            </select>
          )
        })()}
      </div>
      {(role === 'ADMIN' || role === 'MANAGER') && (
        <>
          <div>
            <label className="block text-sm font-medium text-gray-700">Department</label>
            <select value={departmentId} onChange={e=>setDepartmentId(e.target.value)} className="mt-1 w-full border rounded p-2">
              {departments.map(d => <option key={d.id} value={d.id}>{d.name}</option>)}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Assign To</label>
            <select value={assignedToId} onChange={e=>setAssignedToId(e.target.value)} className="mt-1 w-full border rounded p-2">
              <option value="">Unassigned</option>
              {agents.map(a => <option key={a.id} value={a.id}>{a.fullName || a.email}</option>)}
            </select>
          </div>
        </>
      )}
      <div className="flex justify-end">
        <button type="submit" disabled={loading} className="bg-gray-800 hover:bg-black text-white px-4 py-2 rounded">
          {loading ? 'Saving...' : 'Save'}
        </button>
      </div>
    </form>
  )
}
