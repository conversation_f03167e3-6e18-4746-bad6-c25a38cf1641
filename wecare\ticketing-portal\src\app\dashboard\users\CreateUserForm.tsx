"use client"

import { useEffect, useState } from 'react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'

type Project = { id: string; name: string }
type Department = { id: string; name: string }

type Props = {}

export default function CreateUserForm(_: Props) {
  const [projects, setProjects] = useState<Project[]>([])
  const [departments, setDepartments] = useState<Department[]>([])
  const [email, setEmail] = useState('')
  const [fullName, setFullName] = useState('')
  const [password, setPassword] = useState('')
  const [role, setRole] = useState<'ADMIN'|'AGENT'|'CUSTOMER'>('CUSTOMER')
  const [selectedProjects, setSelectedProjects] = useState<string[]>([])
  const [selectedDepartments, setSelectedDepartments] = useState<string[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')

  useEffect(() => {
    (async () => {
      try {
        const [prjRes, deptRes] = await Promise.all([
          fetch('/api/projects?all=1'),
          fetch('/api/departments'),
        ])
        const prjData = await prjRes.json()
        const deptData = await deptRes.json()
        if (prjRes.ok) {
          setProjects(prjData.projects || [])
        } else {
          setError(prjData.error || 'Failed to load projects')
        }
        if (deptRes.ok) {
          setDepartments(deptData.departments || [])
        } else if (!error) {
          setError(deptData.error || 'Failed to load departments')
        }
      } catch {
        setError('Failed to load projects/departments')
      }
    })()
  }, [])

  const toggleProject = (id: string) => {
    setSelectedProjects(prev => prev.includes(id) ? prev.filter(x => x !== id) : [...prev, id])
  }
  const toggleDepartment = (id: string) => {
    setSelectedDepartments(prev => prev.includes(id) ? prev.filter(x => x !== id) : [...prev, id])
  }

  const submit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')
    setSuccess('')
    try {
      const res = await fetch('/api/users', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email, fullName, password, role, projectIds: selectedProjects, departmentIds: selectedDepartments }),
      })
      const data = await res.json()
      if (!res.ok) {
        setError(data.error || 'Failed to create user')
        return
      }
      setSuccess('User created')
      setEmail(''); setFullName(''); setPassword(''); setRole('CUSTOMER'); setSelectedProjects([]); setSelectedDepartments([])
    } catch {
      setError('Unexpected error')
    } finally {
      setLoading(false)
    }
  }

  return (
    <form onSubmit={submit} className="space-y-4">
      {error && <div className="bg-red-50 text-red-700 border border-red-200 p-3 rounded">{error}</div>}
      {success && <div className="bg-green-50 text-green-700 border border-green-200 p-3 rounded">{success}</div>}
      <div>
        <label className="block text-sm font-medium text-gray-700">Email</label>
        <Input type="email" value={email} onChange={e=>setEmail(e.target.value)} required className="mt-1" />
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700">Full Name</label>
        <Input value={fullName} onChange={e=>setFullName(e.target.value)} className="mt-1" />
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700">Password</label>
        <Input type="password" value={password} onChange={e=>setPassword(e.target.value)} required className="mt-1" />
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700">Role</label>
        <select value={role} onChange={e=>setRole(e.target.value as any)} className="mt-1 w-full border rounded p-2">
          <option value="CUSTOMER">Customer</option>
          <option value="AGENT">Agent</option>
          <option value="MANAGER">Manager</option>
          <option value="ADMIN">Admin</option>
        </select>
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">Assign Projects</label>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 p-3 border rounded">
          {projects.map(p => (
            <label key={p.id} className="flex items-center gap-2 text-sm">
              <input type="checkbox" checked={selectedProjects.includes(p.id)} onChange={() => toggleProject(p.id)} />
              <span>{p.name}</span>
            </label>
          ))}
          {projects.length === 0 && <div className="text-xs text-gray-500">No projects found</div>}
        </div>
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">Assign Departments</label>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 p-3 border rounded">
          {departments.map(d => (
            <label key={d.id} className="flex items-center gap-2 text-sm">
              <input type="checkbox" checked={selectedDepartments.includes(d.id)} onChange={() => toggleDepartment(d.id)} />
              <span>{d.name}</span>
            </label>
          ))}
          {departments.length === 0 && <div className="text-xs text-gray-500">No departments found</div>}
        </div>
      </div>
      <div className="flex gap-2">
        <Button type="submit" disabled={loading}>{loading ? 'Creating...' : 'Create User'}</Button>
      </div>
    </form>
  )
}

