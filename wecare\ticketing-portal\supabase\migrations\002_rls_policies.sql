-- Enable Row Level Security
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE tickets ENABLE ROW LEVEL SECURITY;
ALTER TABLE ticket_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE email_threads ENABLE ROW LEVEL SECURITY;
ALTER TABLE ticket_attachments ENABLE ROW LEVEL SECURITY;

-- Organizations policies
CREATE POLICY "Users can view their organization" ON organizations
    FOR SELECT USING (
        id IN (
            SELECT organization_id FROM profiles WHERE id = auth.uid()
        )
    );

CREATE POLICY "Admins can manage their organization" ON organizations
    FOR ALL USING (
        id IN (
            SELECT organization_id FROM profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Profiles policies
CREATE POLICY "Users can view profiles in their organization" ON profiles
    FOR SELECT USING (
        organization_id IN (
            SELECT organization_id FROM profiles WHERE id = auth.uid()
        ) OR id = auth.uid()
    );

CREATE POLICY "Users can update their own profile" ON profiles
    FOR UPDATE USING (id = auth.uid());

CREATE POLICY "Ad<PERSON> can manage profiles in their organization" ON profiles
    FOR ALL USING (
        organization_id IN (
            SELECT organization_id FROM profiles 
            WHERE id = auth.uid() AND role IN ('admin', 'agent')
        )
    );

-- Tickets policies
CREATE POLICY "Customers can view their own tickets" ON tickets
    FOR SELECT USING (
        customer_id = auth.uid() OR
        assigned_agent_id = auth.uid() OR
        organization_id IN (
            SELECT organization_id FROM profiles 
            WHERE id = auth.uid() AND role IN ('admin', 'agent')
        )
    );

CREATE POLICY "Customers can create tickets" ON tickets
    FOR INSERT WITH CHECK (
        customer_id = auth.uid() AND
        organization_id IN (
            SELECT organization_id FROM profiles WHERE id = auth.uid()
        )
    );

CREATE POLICY "Agents and admins can manage tickets in their organization" ON tickets
    FOR ALL USING (
        organization_id IN (
            SELECT organization_id FROM profiles 
            WHERE id = auth.uid() AND role IN ('admin', 'agent')
        )
    );

CREATE POLICY "Customers can update their own tickets" ON tickets
    FOR UPDATE USING (
        customer_id = auth.uid() AND
        status NOT IN ('resolved', 'closed')
    );

-- Ticket messages policies
CREATE POLICY "Users can view messages for accessible tickets" ON ticket_messages
    FOR SELECT USING (
        ticket_id IN (
            SELECT id FROM tickets WHERE
            customer_id = auth.uid() OR
            assigned_agent_id = auth.uid() OR
            organization_id IN (
                SELECT organization_id FROM profiles 
                WHERE id = auth.uid() AND role IN ('admin', 'agent')
            )
        ) AND (
            NOT is_internal OR
            EXISTS (
                SELECT 1 FROM profiles 
                WHERE id = auth.uid() AND role IN ('admin', 'agent')
            )
        )
    );

CREATE POLICY "Users can create messages for accessible tickets" ON ticket_messages
    FOR INSERT WITH CHECK (
        author_id = auth.uid() AND
        ticket_id IN (
            SELECT id FROM tickets WHERE
            customer_id = auth.uid() OR
            assigned_agent_id = auth.uid() OR
            organization_id IN (
                SELECT organization_id FROM profiles 
                WHERE id = auth.uid() AND role IN ('admin', 'agent')
            )
        )
    );

-- Email threads policies
CREATE POLICY "Agents and admins can view email threads" ON email_threads
    FOR SELECT USING (
        ticket_id IN (
            SELECT id FROM tickets WHERE
            organization_id IN (
                SELECT organization_id FROM profiles 
                WHERE id = auth.uid() AND role IN ('admin', 'agent')
            )
        )
    );

CREATE POLICY "Agents and admins can manage email threads" ON email_threads
    FOR ALL USING (
        ticket_id IN (
            SELECT id FROM tickets WHERE
            organization_id IN (
                SELECT organization_id FROM profiles 
                WHERE id = auth.uid() AND role IN ('admin', 'agent')
            )
        )
    );

-- Ticket attachments policies
CREATE POLICY "Users can view attachments for accessible tickets" ON ticket_attachments
    FOR SELECT USING (
        ticket_id IN (
            SELECT id FROM tickets WHERE
            customer_id = auth.uid() OR
            assigned_agent_id = auth.uid() OR
            organization_id IN (
                SELECT organization_id FROM profiles 
                WHERE id = auth.uid() AND role IN ('admin', 'agent')
            )
        )
    );

CREATE POLICY "Users can upload attachments to accessible tickets" ON ticket_attachments
    FOR INSERT WITH CHECK (
        uploaded_by = auth.uid() AND
        ticket_id IN (
            SELECT id FROM tickets WHERE
            customer_id = auth.uid() OR
            assigned_agent_id = auth.uid() OR
            organization_id IN (
                SELECT organization_id FROM profiles 
                WHERE id = auth.uid() AND role IN ('admin', 'agent')
            )
        )
    );
