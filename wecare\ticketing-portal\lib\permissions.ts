import { prisma } from '@/lib/prisma'

export type PermissionKey =
  | 'DASHBOARD_VIEW'
  | 'TICKETS_VIEW'
  | 'TICKETS_UPDATE'
  | 'TICKETS_ASSIGN'
  | 'REPORTS_VIEW'
  | 'DEPARTMENTS_MANAGE'
  | 'PROJECTS_MANAGE'
  | 'AUDIT_VIEW'

export const ALL_PERMISSIONS: PermissionKey[] = [
  'DASHBOARD_VIEW',
  'TICKETS_VIEW',
  'TICKETS_UPDATE',
  'TICKETS_ASSIGN',
  'REPORTS_VIEW',
  'DEPARTMENTS_MANAGE',
  'PROJECTS_MANAGE',
  'AUDIT_VIEW',
]

export type AuthUserLite = {
  id: string
  role: 'ADMIN' | 'MANAGER' | 'AGENT' | 'CUSTOMER'
  organizationId: string
}

export async function getUserPermissions(user: AuthUserLite): Promise<Set<PermissionKey>> {
  // Admins implicitly have all permissions
  if (user.role === 'ADMIN') return new Set<PermissionKey>(ALL_PERMISSIONS)

  // Everyone else: only what Admin explicitly assigned
  const rows = await prisma.userPermission.findMany({
    where: { userId: user.id, organizationId: user.organizationId },
    select: { permission: true },
  })
  return new Set(rows.map(r => r.permission as PermissionKey))
}

export function hasPermission(perms: Set<PermissionKey>, key: PermissionKey) {
  return perms.has(key)
}

