import { cookies } from 'next/headers'
import { verifyToken } from '@/lib/auth-new'
import { prisma } from '@/lib/prisma'
import DepartmentManager from './DepartmentManager'

export default async function DepartmentsPage() {
  const cookieStore = await cookies()
  const token = cookieStore.get('auth-token')?.value
  const user = token ? verifyToken(token) : null
  if (!user) return <div className="p-6">Not authorized</div>

  let allowed = user.role === 'ADMIN'
  if (!allowed) {
    const has = await prisma.userPermission.findFirst({
      where: { userId: user.id, organizationId: user.organizationId, permission: 'DEPARTMENTS_MANAGE' },
      select: { userId: true }
    })
    allowed = !!has
  }
  if (!allowed) return <div className="p-6">Not authorized</div>

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Departments</h1>
        <p className="mt-1 text-sm text-gray-600">Create, edit, and delete departments.</p>
      </div>
      <DepartmentManager />
    </div>
  )
}

