import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  console.log('Login API called')
  try {
    const { email, password } = await request.json()

    if (!email || !password) {
      return NextResponse.json(
        { error: 'Email and password are required' },
        { status: 400 }
      )
    }

    // Simple demo authentication
    if (email === '<EMAIL>' && password === 'demo123') {
      const response = NextResponse.json({
        success: true,
        user: {
          id: '1',
          email: '<EMAIL>',
          fullName: 'Demo User',
          role: 'ADMIN',
        },
      })

      // Set a simple auth cookie
      response.cookies.set('auth-token', 'demo-token', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        maxAge: 60 * 60 * 24 * 7, // 7 days
      })

      return response
    }

    return NextResponse.json(
      { error: 'Invalid email or password' },
      { status: 401 }
    )
  } catch (error) {
    console.error('Login error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
