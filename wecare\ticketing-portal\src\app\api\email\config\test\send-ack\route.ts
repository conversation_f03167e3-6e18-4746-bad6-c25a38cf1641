import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { verifyToken } from '@/lib/auth-new'

export const runtime = 'nodejs'

export async function POST(req: NextRequest) {
  try {
    const token = req.cookies.get('auth-token')?.value
    const user = token ? verifyToken(token) : null
    if (!user) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    if (user.role !== 'ADMIN') return NextResponse.json({ error: 'Forbidden' }, { status: 403 })

    const body = await req.json().catch(() => null)
    const to: string = String(body?.to || '').trim()
    const ticketNumber: string = body?.ticketNumber ? String(body.ticketNumber) : `TKT-TEST-${Math.random().toString(36).slice(2, 8).toUpperCase()}`
    if (!to) return NextResponse.json({ error: 'Missing to' }, { status: 400 })

    const org = await prisma.organization.findUnique({ where: { id: user.organizationId }, select: { name: true, settings: true } })
    const cfg = (org?.settings as any)?.emailConfig || {}
    const smtp = cfg?.smtp || {}

    if (!smtp.host || !smtp.port || !smtp.fromEmail) {
      return NextResponse.json({ error: 'SMTP configuration incomplete. Please set host, port, fromEmail (and auth if required).' }, { status: 400 })
    }

    // Dynamic import to avoid build-time dependency when not installed
    let nodemailer: any
    try {
      // eslint-disable-next-line @typescript-eslint/no-var-requires
      nodemailer = (await import('nodemailer')).default || (await import('nodemailer'))
    } catch (e) {
      return NextResponse.json({ error: 'nodemailer is not installed. Please run: npm install nodemailer' }, { status: 501 })
    }

    const transporter = nodemailer.createTransport({
      host: smtp.host,
      port: Number(smtp.port),
      secure: !!smtp.secure,
      auth: smtp.user && smtp.pass ? { user: smtp.user, pass: smtp.pass } : undefined,
    })

    const fromName = smtp.fromName || org?.name || 'WeCare Support'
    const fromEmail = smtp.fromEmail

    const subject = `WeCare Ticket Acknowledgement — ${ticketNumber}`
    const text = [
      `Hello,`,
      ``,
      `This is a test acknowledgement email from WeCare.`,
      `Ticket number: ${ticketNumber}`,
      ``,
      `We have received your request and will get back to you shortly.`,
      `Please reference ${ticketNumber} in future communications.`,
      ``,
      `— ${fromName}`,
    ].join('\n')

    const html = `<!doctype html><html><body style="font-family:system-ui,Segoe UI,Roboto,Arial,sans-serif;color:#0f172a">`+
      `<p>Hello,</p>`+
      `<p>This is a <strong>test acknowledgement</strong> email from WeCare.</p>`+
      `<p><strong>Ticket number:</strong> ${ticketNumber}</p>`+
      `<p>We have received your request and will get back to you shortly.<br/>Please reference <strong>${ticketNumber}</strong> in future communications.</p>`+
      `<p>— ${fromName}</p>`+
      `</body></html>`

    const info = await transporter.sendMail({
      from: { name: fromName, address: fromEmail },
      to,
      subject,
      text,
      html,
    })

    return NextResponse.json({ ok: true, messageId: info?.messageId })
  } catch (e: any) {
    console.error('send test ack error', e)
    return NextResponse.json({ error: 'Internal error' }, { status: 500 })
  }
}

