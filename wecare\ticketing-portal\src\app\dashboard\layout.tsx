import { ReactNode } from 'react'
import Link from 'next/link'
import { cookies } from 'next/headers'
import { redirect } from 'next/navigation'
import LogoutButton from '@/components/LogoutButton'
import { Sidebar } from '@/components/dashboard/sidebar'
import { verifyToken } from '@/lib/auth-new'

export default async function DashboardLayout({ children }: { children: ReactNode }) {
  const cookieStore = await cookies()
  const token = cookieStore.get('auth-token')?.value
  const user = token ? verifyToken(token) : null
  if (!user) redirect('/auth/login')

  return (
    <div className="min-h-screen bg-slate-50">
      <header className="bg-slate-900 text-white border-b border-slate-800">
        <div className="px-4 sm:px-6 lg:px-8 h-14 flex items-center justify-between">
          <Link href="/dashboard" className="text-base font-semibold tracking-tight">WeCare</Link>
          <div className="flex items-center gap-3 text-sm">
            <span className="hidden sm:inline text-slate-200">{user.fullName || user.email}</span>
            <LogoutButton className="px-3 py-1.5 rounded bg-slate-800 hover:bg-slate-700 border border-slate-700" />
          </div>
        </div>
      </header>
      <div className="grid grid-cols-1 md:grid-cols-[16rem_1fr] gap-6 p-4 sm:p-6 lg:p-8">
        <Sidebar user={user} />
        <main>
          {children}
        </main>
      </div>
    </div>
  )
}

