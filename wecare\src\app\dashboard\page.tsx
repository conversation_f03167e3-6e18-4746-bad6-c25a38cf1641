export default function DashboardPage() {
  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-6">
          🎉 Dashboard Working!
        </h1>
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Welcome to the Ticketing Portal</h2>
          <p className="text-gray-600">
            The dashboard is now working! This is a simplified version to test the routing.
          </p>
          <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
            <p className="text-green-700">
              ✅ Authentication: Working<br />
              ✅ Routing: Working<br />
              ✅ Dashboard: Working
            </p>
          </div>
          <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h3 className="text-lg font-semibold text-blue-800 mb-2">Demo Credentials</h3>
            <p className="text-blue-700">
              <strong>Email:</strong> <EMAIL><br />
              <strong>Password:</strong> demo123
            </p>
          </div>
          <div className="mt-6">
            <button
              onClick={() => {
                document.cookie = 'auth-token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT'
                window.location.href = '/auth/login'
              }}
              className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg"
            >
              Logout
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
