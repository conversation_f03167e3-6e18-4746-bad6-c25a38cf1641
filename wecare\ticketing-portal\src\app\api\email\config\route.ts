import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { verifyToken } from '@/lib/auth-new'

export const runtime = 'nodejs'

function pick<T extends object, K extends keyof T>(obj: T, keys: K[]): Pick<T, K> {
  const out = {} as any
  for (const k of keys) out[k] = obj[k]
  return out
}

function mergeSettings(oldSettings: any, update: any) {
  const settings = oldSettings && typeof oldSettings === 'object' ? { ...oldSettings } : {}
  const emailConfig = { ...(settings.emailConfig || {}) }
  for (const k of Object.keys(update)) {
    if (update[k] === null || update[k] === undefined) continue
    emailConfig[k] = update[k]
  }
  settings.emailConfig = emailConfig
  return settings
}

export async function GET(req: NextRequest) {
  const token = req.cookies.get('auth-token')?.value
  const user = token ? verifyToken(token) : null
  if (!user) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  if (user.role !== 'ADMIN') return NextResponse.json({ error: 'Forbidden' }, { status: 403 })

  const org = await prisma.organization.findUnique({ where: { id: user.organizationId }, select: { settings: true } })
  const emailConfig = (org?.settings as any)?.emailConfig || {}
  return NextResponse.json({ emailConfig })
}

export async function PATCH(req: NextRequest) {
  const token = req.cookies.get('auth-token')?.value
  const user = token ? verifyToken(token) : null
  if (!user) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  if (user.role !== 'ADMIN') return NextResponse.json({ error: 'Forbidden' }, { status: 403 })

  const body = await req.json().catch(() => null)
  if (!body || typeof body !== 'object') return NextResponse.json({ error: 'Invalid body' }, { status: 400 })

  // Accept a subset of fields for safety
  const allowed = pick(body as any, [
    'webhookSecret',
    'routingMap',
    'defaultProjectId',
    'defaultDepartmentId',
    'rateLimitPerHour',
    'smtp', // { host, port, user, pass, fromEmail, fromName, secure }
  ] as any)

  // Basic validation
  if (allowed.routingMap && typeof allowed.routingMap !== 'object') {
    return NextResponse.json({ error: 'routingMap must be an object' }, { status: 400 })
  }
  if (allowed.smtp && typeof allowed.smtp !== 'object') {
    return NextResponse.json({ error: 'smtp must be an object' }, { status: 400 })
  }

  const org = await prisma.organization.findUnique({ where: { id: user.organizationId }, select: { settings: true } })
  const newSettings = mergeSettings(org?.settings, allowed)

  await prisma.organization.update({ where: { id: user.organizationId }, data: { settings: newSettings } })

  return NextResponse.json({ ok: true })
}

