import { headers } from 'next/headers'

export default async function UserAuditPanel() {
  const hdrs = await headers()
  const host = hdrs.get('host') || 'localhost:3002'
  const proto = hdrs.get('x-forwarded-proto') || 'http'
  const base = `${proto}://${host}`

  const res = await fetch(`${base}/api/audit/users?limit=50`, {
    cache: 'no-store',
    headers: { cookie: hdrs.get('cookie') || '' },
  })
  const data = await res.json().catch(() => ({ logs: [] }))
  const logs = data.logs || []
  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h2 className="font-semibold mb-4">Recent User Changes</h2>
      <div className="overflow-x-auto">
        <table className="min-w-full text-sm">
          <thead>
            <tr>
              <th className="text-left p-2">Time</th>
              <th className="text-left p-2">Action</th>
              <th className="text-left p-2">Actor</th>
              <th className="text-left p-2">Target</th>
            </tr>
          </thead>
          <tbody>
            {logs.map((l: any) => (
              <tr key={l.id} className="border-t">
                <td className="p-2">{new Date(l.createdAt).toLocaleString()}</td>
                <td className="p-2">{l.action}</td>
                <td className="p-2">{l.actor?.fullName || l.actor?.email}</td>
                <td className="p-2">{l.targetUser?.fullName || l.targetUser?.email}</td>
              </tr>
            ))}
            {logs.length === 0 && (
              <tr>
                <td className="p-2 text-gray-500" colSpan={4}>No activity yet</td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  )
}
