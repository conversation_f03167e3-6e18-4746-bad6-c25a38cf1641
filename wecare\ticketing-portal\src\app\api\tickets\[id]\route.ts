import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { verifyToken } from '@/lib/auth-new'
import { getUserPermissions, hasPermission } from '@/lib/permissions'

export async function GET(req: NextRequest, context: { params: Promise<{ id: string }> }) {
  const token = req.cookies.get('auth-token')?.value
  const user = token ? verifyToken(token) : null
  if (!user) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })

  const { params } = context
  const { id } = await params
  const ticket = await prisma.ticket.findUnique({
    where: { id },
    include: {
      createdBy: { select: { fullName: true, email: true } },
      assignedTo: { select: { fullName: true, email: true } },
      messages: { include: { author: { select: { fullName: true, email: true } } }, orderBy: { createdAt: 'asc' } },
    },
  })
  if (!ticket || ticket.organizationId !== user.organizationId) return NextResponse.json({ error: 'Not found' }, { status: 404 })
  if (ticket.projectId) {
    const membership = await prisma.userProject.findUnique({ where: { userId_projectId: { userId: user.id, projectId: ticket.projectId } } })
    if (!membership) return NextResponse.json({ error: 'Not found' }, { status: 404 })
  }
  return NextResponse.json({ ticket })
}

export async function PATCH(req: NextRequest, context: { params: Promise<{ id: string }> }) {
  const token = req.cookies.get('auth-token')?.value
  const user = token ? verifyToken(token) : null
  if (!user) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })

  const body = await req.json()
  const { status, priority, assignedToId, title, description, category, departmentId } = body || {}

  // Load current ticket to compare and to verify org/project access
  const { params } = context
  const { id } = await params
  const current = await prisma.ticket.findUnique({ where: { id } })
  if (!current || current.organizationId !== user.organizationId) {
    return NextResponse.json({ error: 'Not found' }, { status: 404 })
  }
  if (current.projectId) {
    const membership = await prisma.userProject.findUnique({ where: { userId_projectId: { userId: user.id, projectId: current.projectId } } })
    if (!membership) return NextResponse.json({ error: 'Not found' }, { status: 404 })
  }

  // RBAC: determine what this user can update
  const isAdmin = user.role === 'ADMIN'
  const isManager = user.role === 'MANAGER'
  const isAgent = user.role === 'AGENT'
  const isCustomer = user.role === 'CUSTOMER'

  if (isCustomer) {
    return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
  }

  const perms = await getUserPermissions({ id: user.id, role: user.role, organizationId: user.organizationId })

  // Validate department change (only for admin/manager)
  if ((isAdmin || isManager) && typeof departmentId !== 'undefined') {
    if (departmentId === null) {
      // Allow clearing department
    } else {
      const dept = await prisma.department.findFirst({ where: { id: departmentId, organizationId: user.organizationId }, select: { id: true } })
      if (!dept) return NextResponse.json({ error: 'Invalid department' }, { status: 400 })
    }
  }

  // Validate assignee if provided (only for admin/manager)
  if ((isAdmin || isManager) && typeof assignedToId !== 'undefined' && assignedToId) {
    const assignee = await prisma.user.findUnique({ where: { id: assignedToId } })
    if (!assignee || assignee.organizationId !== user.organizationId) return NextResponse.json({ error: 'Invalid assignee' }, { status: 400 })
    if (current.projectId) {
      const assigneeProject = await prisma.userProject.findUnique({ where: { userId_projectId: { userId: assignee.id, projectId: current.projectId } } })
      if (!assigneeProject) return NextResponse.json({ error: 'Assignee is not a member of the ticket\'s project' }, { status: 400 })
    }
    const effectiveDeptId = typeof departmentId !== 'undefined' ? departmentId : current.departmentId
    if (effectiveDeptId) {
      const assigneeDept = await prisma.userDepartment.findUnique({ where: { userId_departmentId: { userId: assignee.id, departmentId: effectiveDeptId } } })
      if (!assigneeDept) return NextResponse.json({ error: 'Assignee is not a member of the ticket\'s department' }, { status: 400 })
    }
  }

  // If manager is changing department/assignee, ensure manager belongs to that department (when present)
  if (isManager) {
    const effectiveDeptId = typeof departmentId !== 'undefined' ? departmentId : current.departmentId
    if (effectiveDeptId) {
      const managerDept = await prisma.userDepartment.findUnique({ where: { userId_departmentId: { userId: user.id, departmentId: effectiveDeptId } } })
      if (!managerDept) return NextResponse.json({ error: 'Forbidden: manager not a member of department' }, { status: 403 })
    }
  }

  // Build allowed update payload per role
  // Strict permission checks by role
  if (isAgent) {
    const tryingForbiddenFields = (typeof assignedToId !== 'undefined') || (typeof departmentId !== 'undefined') || !!title || !!description || !!category
    if (tryingForbiddenFields) {
      return NextResponse.json({ error: 'Forbidden: agents cannot modify assignment, department, title, description, or category' }, { status: 403 })
    }
    if (!hasPermission(perms, 'TICKETS_UPDATE')) {
      return NextResponse.json({ error: 'Forbidden: missing permission TICKETS_UPDATE' }, { status: 403 })
    }
    if (status && !['IN_PROGRESS', 'WAITING_FOR_CUSTOMER', 'RESOLVED'].includes(status)) {
      return NextResponse.json({ error: 'Forbidden: status not allowed for agents' }, { status: 403 })
    }
    if (priority === 'URGENT') {
      return NextResponse.json({ error: 'Forbidden: priority URGENT not allowed for agents' }, { status: 403 })
    }
  }
  if (isManager) {
    if (title || description || category) {
      return NextResponse.json({ error: 'Forbidden: managers cannot modify title, description, or category' }, { status: 403 })
    }
    // When attempting assignment/department changes, require TICKETS_ASSIGN
    if ((typeof assignedToId !== 'undefined') || (typeof departmentId !== 'undefined')) {
      if (!hasPermission(perms, 'TICKETS_ASSIGN')) {
        return NextResponse.json({ error: 'Forbidden: missing permission TICKETS_ASSIGN' }, { status: 403 })
      }
    }
  }

  const data: any = {}
  if (status) data.status = status
  if (priority) data.priority = priority

  if (isAdmin || isManager) {
    if (typeof assignedToId !== 'undefined') data.assignedToId = assignedToId || null
    if (typeof departmentId !== 'undefined') data.departmentId = departmentId || null
  }
  if (isAdmin) {
    if (title) data.title = title
    if (description) data.description = description
    if (category) data.category = category
  }

  const updated = await prisma.ticket.update({ where: { id }, data })

  // Build audit message if anything changed
  const changes: string[] = []
  if (status && status !== current.status) changes.push(`Status: ${current.status} → ${status}`)
  if (priority && priority !== current.priority) changes.push(`Priority: ${current.priority} → ${priority}`)
  if ((isAdmin || isManager) && typeof assignedToId !== 'undefined' && assignedToId !== current.assignedToId) changes.push(`Assignee: ${current.assignedToId || 'Unassigned'} → ${assignedToId || 'Unassigned'}`)
  if ((isAdmin || isManager) && typeof departmentId !== 'undefined' && departmentId !== current.departmentId) changes.push(`Department: ${current.departmentId || 'None'} → ${departmentId || 'None'}`)
  if ((isAdmin || isManager) && title && title !== current.title) changes.push('Title updated')
  if ((isAdmin || isManager) && description && description !== current.description) changes.push('Description updated')
  if ((isAdmin || isManager) && category && category !== (current.category || '')) changes.push(`Category: ${current.category || 'None'} → ${category}`)

  if (changes.length) {
    await prisma.ticketMessage.create({
      data: {
        content: `[AUDIT] ${changes.join('; ')} — by ${user.fullName || user.email}`,
        isInternal: true,
        ticketId: current.id,
        authorId: user.id,
      },
    })
  }

  return NextResponse.json({ ticket: updated })
}

