-- Function to generate unique ticket numbers
CREATE OR REPLACE FUNCTION generate_ticket_number()
RETURNS TEXT AS $$
DECLARE
    new_number TEXT;
    counter INTEGER := 1;
BEGIN
    LOOP
        new_number := 'TKT-' || TO_CHAR(NOW(), 'YYYYMMDD') || '-' || LPAD(counter::TEXT, 4, '0');
        
        -- Check if this number already exists
        IF NOT EXISTS (SELECT 1 FROM tickets WHERE ticket_number = new_number) THEN
            RETURN new_number;
        END IF;
        
        counter := counter + 1;
        
        -- Safety check to prevent infinite loop
        IF counter > 9999 THEN
            new_number := 'TKT-' || TO_CHAR(NOW(), 'YYYYMMDD') || '-' || EXTRACT(EPOCH FROM NOW())::INTEGER;
            EXIT;
        END IF;
    END LOOP;
    
    RETURN new_number;
END;
$$ LANGUAGE plpgsql;

-- Function to automatically set ticket number on insert
CREATE OR REPLACE FUNCTION set_ticket_number()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.ticket_number IS NULL OR NEW.ticket_number = '' THEN
        NEW.ticket_number := generate_ticket_number();
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for automatic ticket number generation
CREATE TRIGGER trigger_set_ticket_number
    BEFORE INSERT ON tickets
    FOR EACH ROW
    EXECUTE FUNCTION set_ticket_number();

-- Function to handle user profile creation after signup
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO profiles (id, email, full_name)
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email)
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for automatic profile creation
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION handle_new_user();

-- Function to update ticket status and set resolved_at
CREATE OR REPLACE FUNCTION update_ticket_status(
    ticket_id UUID,
    new_status TEXT,
    user_id UUID
)
RETURNS BOOLEAN AS $$
DECLARE
    old_status TEXT;
    ticket_org_id UUID;
    user_role TEXT;
BEGIN
    -- Get current ticket status and organization
    SELECT status, organization_id INTO old_status, ticket_org_id
    FROM tickets WHERE id = ticket_id;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Ticket not found';
    END IF;
    
    -- Get user role
    SELECT role INTO user_role FROM profiles WHERE id = user_id;
    
    -- Check permissions
    IF user_role NOT IN ('admin', 'agent') THEN
        -- Customers can only reopen their own tickets
        IF NOT EXISTS (
            SELECT 1 FROM tickets 
            WHERE id = ticket_id AND customer_id = user_id
        ) THEN
            RAISE EXCEPTION 'Permission denied';
        END IF;
        
        -- Customers can only change from resolved/closed to open
        IF old_status NOT IN ('resolved', 'closed') OR new_status != 'open' THEN
            RAISE EXCEPTION 'Invalid status change';
        END IF;
    END IF;
    
    -- Update ticket status
    UPDATE tickets 
    SET 
        status = new_status,
        resolved_at = CASE 
            WHEN new_status = 'resolved' THEN NOW()
            WHEN new_status != 'resolved' THEN NULL
            ELSE resolved_at
        END,
        updated_at = NOW()
    WHERE id = ticket_id;
    
    -- Log status change
    INSERT INTO ticket_messages (ticket_id, author_id, content, message_type, is_internal)
    VALUES (
        ticket_id,
        user_id,
        'Status changed from ' || old_status || ' to ' || new_status,
        'status_change',
        false
    );
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to assign ticket to agent
CREATE OR REPLACE FUNCTION assign_ticket(
    ticket_id UUID,
    agent_id UUID,
    assigner_id UUID
)
RETURNS BOOLEAN AS $$
DECLARE
    ticket_org_id UUID;
    agent_org_id UUID;
    assigner_role TEXT;
BEGIN
    -- Get ticket organization
    SELECT organization_id INTO ticket_org_id
    FROM tickets WHERE id = ticket_id;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Ticket not found';
    END IF;
    
    -- Get agent organization and assigner role
    SELECT organization_id INTO agent_org_id
    FROM profiles WHERE id = agent_id AND role IN ('agent', 'admin');
    
    SELECT role INTO assigner_role
    FROM profiles WHERE id = assigner_id;
    
    -- Validate permissions and organization match
    IF assigner_role NOT IN ('admin', 'agent') THEN
        RAISE EXCEPTION 'Permission denied';
    END IF;
    
    IF agent_org_id != ticket_org_id THEN
        RAISE EXCEPTION 'Agent must be in the same organization as the ticket';
    END IF;
    
    -- Update ticket assignment
    UPDATE tickets 
    SET 
        assigned_agent_id = agent_id,
        updated_at = NOW()
    WHERE id = ticket_id;
    
    -- Log assignment
    INSERT INTO ticket_messages (ticket_id, author_id, content, message_type, is_internal)
    VALUES (
        ticket_id,
        assigner_id,
        'Ticket assigned to ' || (SELECT full_name FROM profiles WHERE id = agent_id),
        'assignment',
        false
    );
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
