import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { verifyToken } from '@/lib/auth-new'

export const runtime = 'nodejs'

export async function GET(req: NextRequest) {
  const token = req.cookies.get('auth-token')?.value
  const user = token ? verifyToken(token) : null
  if (!user) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })

  const { searchParams } = new URL(req.url)
  const all = searchParams.get('all') === '1'

  if (all && user.role === 'ADMIN') {
    const projects = await prisma.project.findMany({
      where: { organizationId: user.organizationId },
      orderBy: { name: 'asc' },
    })
    return NextResponse.json({ projects })
  }

  const memberships = await prisma.userProject.findMany({
    where: { userId: user.id },
    select: { project: { select: { id: true, name: true } } },
  })
  const projects = memberships.map(m => m.project)
  return NextResponse.json({ projects })
}

