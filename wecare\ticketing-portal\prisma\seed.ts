import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Seeding database...')

  // Create organization
  const organization = await prisma.organization.upsert({
    where: { domain: 'demo.com' },
    update: {},
    create: {
      name: 'Demo Company',
      domain: 'demo.com',
      settings: {
        allowCustomerRegistration: true,
        defaultTicketPriority: 'MEDIUM',
        businessHours: {
          monday: { start: '09:00', end: '17:00' },
          tuesday: { start: '09:00', end: '17:00' },
          wednesday: { start: '09:00', end: '17:00' },
          thursday: { start: '09:00', end: '17:00' },
          friday: { start: '09:00', end: '17:00' },
        },
      },
    },
  })

  console.log('✅ Created organization:', organization.name)

  // Create users
  const hashedPassword = await bcrypt.hash('demo123', 12)

  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: hashedPassword,
      fullName: 'Admin User',
      role: 'ADMIN',
      organizationId: organization.id,
    },
  })

  const agentUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: hashedPassword,
      fullName: 'Support Agent',
      role: 'AGENT',
      organizationId: organization.id,
    },
  })

  const customerUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: hashedPassword,
      fullName: 'Kamal Gupta',
      role: 'CUSTOMER',
      organizationId: organization.id,
    },
  })

  console.log('✅ Created users')

  // Create projects
  const projectCare = await prisma.project.upsert({
    where: { id: 'seed-project-care' },
    update: {},
    create: {
      id: 'seed-project-care',
      name: 'Customer Care',
      description: 'Customer care and support',
      organizationId: organization.id,
    },
  })
  const projectPayments = await prisma.project.upsert({
    where: { id: 'seed-project-pay' },
    update: {},
    create: {
      id: 'seed-project-pay',
      name: 'Payments',
      description: 'Payments and billing',
      organizationId: organization.id,
    },
  })

  // Create departments
  const deptSupport = await prisma.department.upsert({
    where: { id: 'seed-dept-support' },
    update: {},
    create: {
      id: 'seed-dept-support',
      name: 'Customer Support',
      description: 'General support inquiries',
      organizationId: organization.id,
    },
  })
  const deptBilling = await prisma.department.upsert({
    where: { id: 'seed-dept-billing' },
    update: {},
    create: {
      id: 'seed-dept-billing',
      name: 'Billing',
      description: 'Payments and billing related issues',
      organizationId: organization.id,
    },
  })


  // Assign users to projects
  await prisma.userProject.upsert({
    where: { userId_projectId: { userId: adminUser.id, projectId: projectCare.id } },
    update: {},
    create: { userId: adminUser.id, projectId: projectCare.id },
  })
  await prisma.userProject.upsert({
    where: { userId_projectId: { userId: adminUser.id, projectId: projectPayments.id } },
    update: {},
    create: { userId: adminUser.id, projectId: projectPayments.id },
  })
  await prisma.userProject.upsert({
    where: { userId_projectId: { userId: agentUser.id, projectId: projectCare.id } },
    update: {},
    create: { userId: agentUser.id, projectId: projectCare.id },
  })
  await prisma.userProject.upsert({
    where: { userId_projectId: { userId: agentUser.id, projectId: projectPayments.id } },
    update: {},
    create: { userId: agentUser.id, projectId: projectPayments.id },
  })
  await prisma.userProject.upsert({
    where: { userId_projectId: { userId: customerUser.id, projectId: projectCare.id } },
    update: {},
    create: { userId: customerUser.id, projectId: projectCare.id },
  })

  // Assign users to departments
  await prisma.userDepartment.upsert({
    where: { userId_departmentId: { userId: adminUser.id, departmentId: deptSupport.id } },
    update: {},
    create: { userId: adminUser.id, departmentId: deptSupport.id },
  })
  await prisma.userDepartment.upsert({
    where: { userId_departmentId: { userId: adminUser.id, departmentId: deptBilling.id } },
    update: {},
    create: { userId: adminUser.id, departmentId: deptBilling.id },
  })
  await prisma.userDepartment.upsert({
    where: { userId_departmentId: { userId: agentUser.id, departmentId: deptSupport.id } },
    update: {},
    create: { userId: agentUser.id, departmentId: deptSupport.id },
  })
  await prisma.userDepartment.upsert({
    where: { userId_departmentId: { userId: agentUser.id, departmentId: deptBilling.id } },
    update: {},
    create: { userId: agentUser.id, departmentId: deptBilling.id },
  })
  await prisma.userDepartment.upsert({
    where: { userId_departmentId: { userId: customerUser.id, departmentId: deptSupport.id } },
    update: {},
    create: { userId: customerUser.id, departmentId: deptSupport.id },
  })

  // Create sample tickets
  const tickets = [
    {
      ticketNumber: 'TKT-********-0001',
      title: 'Login issues with mobile app',
      description: 'I am unable to log into the mobile application. The app crashes when I enter my credentials.',
      status: 'OPEN' as const,
      priority: 'HIGH' as const,
      category: 'Technical Support',
      organizationId: organization.id,
      createdById: customerUser.id,
      assignedToId: agentUser.id,
      projectId: projectCare.id,
      departmentId: deptSupport.id,
    },
    {
      ticketNumber: 'TKT-********-0002',
      title: 'Feature request: Dark mode',
      description: 'It would be great to have a dark mode option in the application for better user experience during night time usage.',
      status: 'IN_PROGRESS' as const,
      priority: 'MEDIUM' as const,
      category: 'Feature Request',
      organizationId: organization.id,
      createdById: customerUser.id,
      assignedToId: agentUser.id,
      projectId: projectCare.id,
      departmentId: deptSupport.id,
    },
    {
      ticketNumber: 'TKT-********-0003',
      title: 'Payment processing error',
      description: 'Getting an error when trying to process payment. Error code: PAY_001. This is blocking our business operations.',
      status: 'RESOLVED' as const,
      priority: 'URGENT' as const,
      category: 'Billing',
      organizationId: organization.id,
      createdById: customerUser.id,
      assignedToId: agentUser.id,
      resolvedAt: new Date(),
      projectId: projectPayments.id,
      departmentId: deptBilling.id,
    },
    {
      ticketNumber: 'TKT-********-0004',
      title: 'Account access issues',
      description: 'Cannot access my account dashboard. Getting a 403 forbidden error.',
      status: 'WAITING_FOR_CUSTOMER' as const,
      priority: 'MEDIUM' as const,
      category: 'Account',
      organizationId: organization.id,
      createdById: customerUser.id,
      assignedToId: agentUser.id,
      projectId: projectCare.id,
      departmentId: deptSupport.id,
    },
    {
      ticketNumber: 'TKT-********-0005',
      title: 'Data export functionality',
      description: 'Need help with exporting data from the platform. The export button is not working.',
      status: 'CLOSED' as const,
      priority: 'LOW' as const,
      category: 'Data Management',
      organizationId: organization.id,
      createdById: customerUser.id,
      assignedToId: agentUser.id,
      resolvedAt: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
      projectId: projectPayments.id,
      departmentId: deptBilling.id,
    },
  ]

  for (const ticketData of tickets) {
    const ticket = await prisma.ticket.upsert({
      where: { ticketNumber: ticketData.ticketNumber },
      update: {},
      create: ticketData,
    })

    // Add some messages to tickets
    await prisma.ticketMessage.create({
      data: {
        content: `Thank you for contacting support regarding "${ticket.title}". We have received your request and will get back to you shortly.`,
        isInternal: false,
        ticketId: ticket.id,
        authorId: agentUser.id,
      },
    })

    if (ticket.status === 'RESOLVED' || ticket.status === 'CLOSED') {
      await prisma.ticketMessage.create({
        data: {
          content: 'This issue has been resolved. Please let us know if you need any further assistance.',
          isInternal: false,
          ticketId: ticket.id,
          authorId: agentUser.id,
        },
      })
    }
  }

  console.log('✅ Created sample tickets with messages')

  console.log('\n🎉 Database seeded successfully!')
  console.log('\n📋 Demo Accounts:')
  console.log('👤 Admin: <EMAIL> / demo123')
  console.log('🎧 Agent: <EMAIL> / demo123')
  console.log('👥 Customer: <EMAIL> / demo123')
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
