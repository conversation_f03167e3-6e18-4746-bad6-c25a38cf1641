import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { verifyToken } from '@/lib/auth-new'

export const runtime = 'nodejs'

export async function PATCH(req: NextRequest, context: { params: Promise<{ id: string }> }) {
  const token = req.cookies.get('auth-token')?.value
  const admin = token ? verifyToken(token) : null
  if (!admin) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  if (admin.role !== 'ADMIN') return NextResponse.json({ error: 'Forbidden' }, { status: 403 })

  const body = await req.json()
  const { role, isActive, projectIds, departmentIds, fullName, permissions } = body || {}

  // Verify user belongs to same org
  const { params } = context
  const { id } = await params
  const target = await prisma.user.findUnique({ where: { id } })
  if (!target || target.organizationId !== admin.organizationId) return NextResponse.json({ error: 'Not found' }, { status: 404 })

  // Capture "before" state for audit
  const beforeProjects = await prisma.userProject.findMany({ where: { userId: target.id }, select: { projectId: true } })
  const beforeDepartments = await prisma.userDepartment.findMany({ where: { userId: target.id }, select: { departmentId: true } })
  const beforePerms = await prisma.userPermission.findMany({ where: { userId: target.id }, select: { permission: true } })
  const beforeProjectIds = beforeProjects.map(p => p.projectId)
  const beforeDepartmentIds = beforeDepartments.map(d => d.departmentId)
  const beforePermissionKeys = beforePerms.map(p => p.permission)

  // Update simple fields
  const updated = await prisma.user.update({
    where: { id },
    data: {
      ...(fullName !== undefined ? { fullName } : {}),
      ...(typeof isActive === 'boolean' ? { isActive } : {}),
      ...(role && ['ADMIN', 'MANAGER', 'AGENT', 'CUSTOMER'].includes(role) ? { role } : {}),
    },
  })

  // Replace project memberships if provided
  if (Array.isArray(projectIds)) {
    await prisma.userProject.deleteMany({ where: { userId: updated.id } })
    const projects = await prisma.project.findMany({ where: { id: { in: projectIds }, organizationId: admin.organizationId }, select: { id: true } })
    for (const p of projects) {
      await prisma.userProject.create({ data: { userId: updated.id, projectId: p.id } })
    }
  }

  // Replace department memberships if provided
  if (Array.isArray(departmentIds)) {
    await prisma.userDepartment.deleteMany({ where: { userId: updated.id } })
    const departments = await prisma.department.findMany({ where: { id: { in: departmentIds }, organizationId: admin.organizationId }, select: { id: true } })
    for (const d of departments) {
      await prisma.userDepartment.create({ data: { userId: updated.id, departmentId: d.id } })
    }
  }

  // Replace permissions if provided (array of PermissionKey)
  if (Array.isArray(permissions)) {
    await prisma.userPermission.deleteMany({ where: { userId: updated.id } })
    const unique = Array.from(new Set(permissions.filter((p: any) => typeof p === 'string')))
    for (const key of unique) {
      await prisma.userPermission.create({
        data: { userId: updated.id, organizationId: admin.organizationId, permission: key as any },
      })
    }
  }

  // Audit logs for changes
  const ip = req.headers.get('x-forwarded-for')?.split(',')[0] || null
  const logs: any[] = []
  if (role && role !== target.role && ['ADMIN','MANAGER','AGENT','CUSTOMER'].includes(role)) {
    logs.push({ action: 'USER_ROLE_CHANGE', before: { role: target.role }, after: { role } })
  }
  if (typeof isActive === 'boolean' && isActive !== target.isActive) {
    logs.push({ action: 'USER_ACTIVATION', before: { isActive: target.isActive }, after: { isActive } })
  }
  if (Array.isArray(projectIds)) {
    const afterProjects = await prisma.userProject.findMany({ where: { userId: updated.id }, select: { projectId: true } })
    const afterProjectIds = afterProjects.map(p => p.projectId)
    logs.push({ action: 'USER_PROJECT_SET', before: { projectIds: beforeProjectIds }, after: { projectIds: afterProjectIds } })
  }
  if (Array.isArray(departmentIds)) {
    const afterDepartments = await prisma.userDepartment.findMany({ where: { userId: updated.id }, select: { departmentId: true } })
    const afterDepartmentIds = afterDepartments.map(d => d.departmentId)
    logs.push({ action: 'USER_DEPARTMENT_SET', before: { departmentIds: beforeDepartmentIds }, after: { departmentIds: afterDepartmentIds } })
  }
  if (Array.isArray(permissions)) {
    const afterPerms = await prisma.userPermission.findMany({ where: { userId: updated.id }, select: { permission: true } })
    const afterPermissionKeys = afterPerms.map(p => p.permission)
    logs.push({ action: 'USER_PERMISSION_SET', before: { permissions: beforePermissionKeys }, after: { permissions: afterPermissionKeys } })
  }

  for (const entry of logs) {
    await prisma.userAuditLog.create({
      data: {
        organizationId: admin.organizationId,
        actorId: admin.id,
        targetUserId: updated.id,
        action: entry.action,
        before: entry.before,
        after: entry.after,
        ip: ip || undefined,
      },
    })
  }

  return NextResponse.json({ user: { id: updated.id, email: updated.email, fullName: updated.fullName, role: updated.role, isActive: updated.isActive } })
}

