import { cookies } from 'next/headers'
import { verifyToken } from '@/lib/auth-new'
import CreateUserForm from './CreateUserForm'
import UserAdminList from './UserAdminList'
import UserAuditPanel from './UserAuditPanel'

export default async function UsersPage() {
  const cookieStore = await cookies()
  const token = cookieStore.get('auth-token')?.value
  const user = token ? verifyToken(token) : null
  if (!user || user.role !== 'ADMIN') {
    return <div className="p-6">Not authorized</div>
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Users</h1>
        <p className="mt-1 text-sm text-gray-600">Create users and manage roles, projects, departments</p>
      </div>
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="font-semibold mb-4">Create New User</h2>
        <CreateUserForm />
      </div>
      <UserAuditPanel />
      <UserAdminList />
    </div>
  )
}

