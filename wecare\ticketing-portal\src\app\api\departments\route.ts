import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { verifyToken } from '@/lib/auth-new'

export const runtime = 'nodejs'

export async function GET(req: NextRequest) {
  const token = req.cookies.get('auth-token')?.value
  const user = token ? verifyToken(token) : null
  if (!user) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })

  try {
    const { searchParams } = new URL(req.url)
    const myOnly = searchParams.get('my') === '1'

    if (myOnly && user.role !== 'ADMIN') {
      // Return only departments assigned to this user
      const links = await prisma.userDepartment.findMany({
        where: { userId: user.id, department: { organizationId: user.organizationId } },
        select: { department: { select: { id: true, name: true, description: true } } },
        orderBy: { department: { name: 'asc' } },
      })
      return NextResponse.json({ departments: links.map(l => l.department) })
    }

    const departments = await prisma.department.findMany({
      where: { organizationId: user.organizationId },
      select: { id: true, name: true, description: true },
      orderBy: { name: 'asc' },
    })
    return NextResponse.json({ departments })
  } catch (e: any) {
    return NextResponse.json({ error: e?.message || 'Failed to load departments' }, { status: 400 })
  }
}

export async function POST(req: NextRequest) {
  const token = req.cookies.get('auth-token')?.value
  const admin = token ? verifyToken(token) : null
  if (!admin) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  if (admin.role !== 'ADMIN') return NextResponse.json({ error: 'Forbidden' }, { status: 403 })

  try {
    const { name, description } = await req.json()
    if (!name || typeof name !== 'string') {
      return NextResponse.json({ error: 'Name is required' }, { status: 400 })
    }
    const dep = await prisma.department.create({
      data: { name, description: description || null, organizationId: admin.organizationId },
      select: { id: true, name: true, description: true },
    })
    return NextResponse.json({ department: dep }, { status: 201 })
  } catch (e: any) {
    return NextResponse.json({ error: e?.message || 'Failed to create department' }, { status: 400 })
  }
}

