import { cookies } from 'next/headers'
import Link from 'next/link'
import { prisma } from '@/lib/prisma'
import { verifyToken } from '@/lib/auth-new'
import { Button } from '@/components/ui/button'
import { getUserPermissions, hasPermission } from '@/lib/permissions'

export default async function TicketsPage({ searchParams }: { searchParams?: Record<string, string | string[] | undefined> }) {
  const cookieStore = await cookies()
  const token = cookieStore.get('auth-token')?.value
  const user = token ? verifyToken(token) : null
  if (!user) {
    return <div className="p-6">Unauthorized</div>
  }

  // Enforce permission for non-admins
  if (user.role !== 'ADMIN') {
    const perms = await getUserPermissions({ id: user.id, role: user.role, organizationId: user.organizationId })
    if (!hasPermission(perms, 'TICKETS_VIEW')) {
      return <div className="p-6">Forbidden</div>
    }
  }

  const memberships = await prisma.userProject.findMany({ where: { userId: user.id }, select: { projectId: true } })
  const projectIds = memberships.map(m => m.projectId)
  const deptLinks = await prisma.userDepartment.findMany({ where: { userId: user.id }, select: { departmentId: true } })
  const departmentIds = deptLinks.map(d => d.departmentId)

  // Filters from URL
  const q = typeof searchParams?.q === 'string' ? searchParams!.q.trim() : ''
  const status = typeof searchParams?.status === 'string' ? searchParams!.status.trim() : ''
  const priority = typeof searchParams?.priority === 'string' ? searchParams!.priority.trim() : ''
  const projectId = typeof searchParams?.projectId === 'string' ? searchParams!.projectId.trim() : ''
  const departmentId = typeof searchParams?.departmentId === 'string' ? searchParams!.departmentId.trim() : ''

  // Lists for selects (scoped)
  const [projectsList, departmentsList] = await Promise.all([
    user.role === 'ADMIN'
      ? prisma.project.findMany({ where: { organizationId: user.organizationId }, select: { id: true, name: true } })
      : prisma.project.findMany({ where: { id: { in: projectIds.length ? projectIds : ['__none__'] } }, select: { id: true, name: true } }),
    user.role === 'ADMIN'
      ? prisma.department.findMany({ where: { organizationId: user.organizationId }, select: { id: true, name: true } })
      : prisma.department.findMany({ where: { id: { in: departmentIds.length ? departmentIds : ['__none__'] } }, select: { id: true, name: true } }),
  ])

  const baseWhere: any = user.role === 'ADMIN' ? { organizationId: user.organizationId } : {
    organizationId: user.organizationId,
    projectId: { in: projectIds.length ? projectIds : ['__none__'] },
    departmentId: { in: departmentIds.length ? departmentIds : ['__none__'] },
  }

  const where: any = {
    ...baseWhere,
    ...(q ? { OR: [
      { title: { contains: q, mode: 'insensitive' } },
      { description: { contains: q, mode: 'insensitive' } },
      { ticketNumber: { contains: q, mode: 'insensitive' } },
    ] } : {}),
    ...(status ? { status } : {}),
    ...(priority ? { priority } : {}),
    ...(projectId ? { projectId } : {}),
    ...(departmentId ? { departmentId } : {}),
  }

  const tickets = await prisma.ticket.findMany({
    where,
    include: { createdBy: { select: { fullName: true, email: true } }, assignedTo: { select: { fullName: true, email: true } }, project: { select: { name: true } } },
    orderBy: { createdAt: 'desc' },
    take: 100,
  })
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Tickets</h1>
          <p className="mt-1 text-sm text-gray-600">Manage and track your support tickets</p>
        </div>
        <Link href="/dashboard/tickets/new">
          <Button>New Ticket</Button>
        </Link>
      </div>
      <form method="GET" className="bg-white rounded-lg shadow p-4 space-y-3">
        <div className="grid grid-cols-1 md:grid-cols-6 gap-3">
          <div className="md:col-span-2">
            <label className="block text-xs text-gray-600">Search</label>
            <input type="text" name="q" defaultValue={q} placeholder="Title, description, or ticket #" className="mt-1 w-full border rounded px-3 py-2" />
          </div>
          <div>
            <label className="block text-xs text-gray-600">Status</label>
            <select name="status" defaultValue={status} className="mt-1 w-full border rounded px-3 py-2">
              <option value="">All</option>
              {['OPEN','IN_PROGRESS','WAITING_FOR_CUSTOMER','WAITING_FOR_AGENT','RESOLVED','CLOSED','CANCELLED'].map(s => (
                <option key={s} value={s}>{s.replaceAll('_',' ')}</option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-xs text-gray-600">Priority</label>
            <select name="priority" defaultValue={priority} className="mt-1 w-full border rounded px-3 py-2">
              <option value="">All</option>
              {['LOW','MEDIUM','HIGH','URGENT'].map(p => (
                <option key={p} value={p}>{p}</option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-xs text-gray-600">Project</label>
            <select name="projectId" defaultValue={projectId} className="mt-1 w-full border rounded px-3 py-2">
              <option value="">All</option>
              {projectsList.map((p:any) => (
                <option key={p.id} value={p.id}>{p.name}</option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-xs text-gray-600">Department</label>
            <select name="departmentId" defaultValue={departmentId} className="mt-1 w-full border rounded px-3 py-2">
              <option value="">All</option>
              {departmentsList.map((d:any) => (
                <option key={d.id} value={d.id}>{d.name}</option>
              ))}
            </select>
          </div>
          <div className="flex items-end gap-2">
            <button type="submit" className="inline-flex items-center justify-center rounded bg-blue-600 text-white hover:bg-blue-700 h-10 px-4 py-2">Apply</button>
            <a href="/dashboard/tickets" className="inline-flex items-center justify-center rounded border border-gray-300 text-gray-700 hover:bg-gray-50 h-10 px-4 py-2">Reset</a>
          </div>
        </div>
      </form>
      <div className="bg-white rounded-lg shadow">
        <div className="grid grid-cols-7 text-xs font-semibold text-gray-500 p-3 border-b">
          <div>No.</div><div>Title</div><div>Status</div><div>Priority</div><div>Project</div><div>Created By</div><div className="text-right pr-2">Actions</div>
        </div>
        <ul className="divide-y">
          {tickets.map(t => (
            <li key={t.id} className="grid grid-cols-7 p-3 text-sm items-center">
              <div className="font-medium text-blue-700">{t.ticketNumber}</div>
              <div className="truncate">{t.title}</div>
              <div>{t.status}</div>
              <div>{t.priority}</div>
              <div>{t.project?.name || '-'}</div>
              <div>{t.createdBy.fullName || t.createdBy.email}</div>
              <div className="text-right">
                <Link href={`/dashboard/tickets/${t.id}`} className="inline-flex items-center gap-1 text-blue-600 hover:text-blue-700" title="View lifecycle">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5"><path d="M12 5c-7 0-10 7-10 7s3 7 10 7 10-7 10-7-3-7-10-7zm0 12a5 5 0 1 1 0-10 5 5 0 0 1 0 10zm0-8a3 3 0 1 0 0 6 3 3 0 0 0 0-6z"/></svg>
                  <span className="sr-only">View</span>
                </Link>
              </div>
            </li>
          ))}
          {tickets.length === 0 && (
            <li className="p-4 text-sm text-gray-500">No tickets in your projects.</li>
          )}
        </ul>
      </div>
    </div>
  )
}
