import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { verifyToken } from '@/lib/auth-new'

export const runtime = 'nodejs'

export async function PATCH(req: NextRequest, context: { params: Promise<{ id: string }> }) {
  const token = req.cookies.get('auth-token')?.value
  const admin = token ? verifyToken(token) : null
  if (!admin) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  if (admin.role !== 'ADMIN') return NextResponse.json({ error: 'Forbidden' }, { status: 403 })

  try {
    const { name, description } = await req.json()
    const { params } = context
    const { id } = await params
    const dep = await prisma.department.findUnique({ where: { id } })
    if (!dep || dep.organizationId !== admin.organizationId) return NextResponse.json({ error: 'Not found' }, { status: 404 })

    const updated = await prisma.department.update({
      where: { id: dep.id },
      data: {
        ...(typeof name === 'string' && name.trim() ? { name } : {}),
        ...(typeof description !== 'undefined' ? { description: description || null } : {}),
      },
      select: { id: true, name: true, description: true },
    })
    return NextResponse.json({ department: updated })
  } catch (e: any) {
    return NextResponse.json({ error: e?.message || 'Failed to update department' }, { status: 400 })
  }
}

export async function DELETE(req: NextRequest, context: { params: Promise<{ id: string }> }) {
  const token = req.cookies.get('auth-token')?.value
  const admin = token ? verifyToken(token) : null
  if (!admin) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  if (admin.role !== 'ADMIN') return NextResponse.json({ error: 'Forbidden' }, { status: 403 })

  try {
    const { params } = context
    const { id } = await params
    const dep = await prisma.department.findUnique({ where: { id } })
    if (!dep || dep.organizationId !== admin.organizationId) return NextResponse.json({ error: 'Not found' }, { status: 404 })

    const ticketCount = await prisma.ticket.count({ where: { departmentId: dep.id } })
    if (ticketCount > 0) {
      return NextResponse.json({ error: 'Cannot delete department with existing tickets' }, { status: 400 })
    }

    // Remove user memberships then delete
    await prisma.userDepartment.deleteMany({ where: { departmentId: dep.id } })
    await prisma.department.delete({ where: { id: dep.id } })

    return NextResponse.json({ success: true })
  } catch (e: any) {
    return NextResponse.json({ error: e?.message || 'Failed to delete department' }, { status: 400 })
  }
}

