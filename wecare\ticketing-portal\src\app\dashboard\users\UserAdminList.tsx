"use client"

import { useEffect, useMemo, useState } from 'react'
import { Button } from '@/components/ui/button'

type PermissionKey =
  | 'DASHBOARD_VIEW'
  | 'TICKETS_VIEW'
  | 'TICKETS_UPDATE'
  | 'TICKETS_ASSIGN'
  | 'REPORTS_VIEW'
  | 'DEPARTMENTS_MANAGE'
  | 'PROJECTS_MANAGE'
  | 'AUDIT_VIEW'

const ALL_PERMISSIONS: { key: PermissionKey; label: string }[] = [
  { key: 'DASHBOARD_VIEW', label: 'Dashboard View' },
  { key: 'TICKETS_VIEW', label: 'Tickets: View' },
  { key: 'TICKETS_UPDATE', label: 'Tickets: Update' },
  { key: 'TICKETS_ASSIGN', label: 'Tickets: Assign' },
  { key: 'REPORTS_VIEW', label: 'Reports: View' },
  { key: 'DEPARTMENTS_MANAGE', label: 'Departments: Manage' },
  { key: 'PROJECTS_MANA<PERSON>', label: 'Projects: Manage' },
  { key: 'AUDIT_VIEW', label: 'Audit: View' },
]

type User = {
  id: string
  email: string
  fullName: string | null
  role: 'ADMIN' | 'MANAGER' | 'AGENT' | 'CUSTOMER'
  isActive: boolean
  userProjects: { project: { id: string, name: string } }[]
  userDepartments: { department: { id: string, name: string } }[]
  userPermissions: { permission: PermissionKey }[]
}

type Project = { id: string; name: string }
type Department = { id: string; name: string }

export default function UserAdminList() {
  const [users, setUsers] = useState<User[]>([])
  const [projects, setProjects] = useState<Project[]>([])
  const [departments, setDepartments] = useState<Department[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [savingId, setSavingId] = useState<string | null>(null)

  const load = async () => {
    setLoading(true)
    setError('')
    try {
      const [uRes, pRes, dRes] = await Promise.all([
        fetch('/api/users'),
        fetch('/api/projects?all=1'),
        fetch('/api/departments'),
      ])
      const [uData, pData, dData] = await Promise.all([uRes.json(), pRes.json(), dRes.json()])
      if (!uRes.ok) throw new Error(uData.error || 'Failed to load users')
      if (!pRes.ok) throw new Error(pData.error || 'Failed to load projects')
      if (!dRes.ok) throw new Error(dData.error || 'Failed to load departments')
      setUsers(uData.users || [])
      setProjects(pData.projects || [])
      setDepartments(dData.departments || [])
    } catch (e: any) {
      setError(e?.message || 'Failed to load')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => { load() }, [])

  const updateUser = async (u: User, updates: Partial<User> & { projectIds?: string[], departmentIds?: string[] }) => {
    setSavingId(u.id)
    setError('')
    try {
      const res = await fetch(`/api/users/${u.id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updates),
      })
      const data = await res.json()
      if (!res.ok) throw new Error(data.error || 'Failed to update user')
      await load()
    } catch (e: any) {
      setError(e?.message || 'Failed to update user')
    } finally {
      setSavingId(null)
    }
  }

  const projectIds = useMemo(() => new Set(projects.map(p => p.id)), [projects])
  const departmentIds = useMemo(() => new Set(departments.map(d => d.id)), [departments])

  if (loading) return <div className="p-4">Loading users...</div>
  if (error) return <div className="p-4 text-red-600">{error}</div>

  return (
    <div className="bg-white rounded-lg shadow">
      <div className="p-4 border-b flex items-center justify-between">
        <h2 className="font-semibold">Manage Users</h2>
        <Button variant="outline" onClick={load}>Refresh</Button>
      </div>
      <div className="p-4 overflow-x-auto">
        <table className="min-w-full text-sm">
          <thead>
            <tr className="text-left text-gray-500">
              <th className="p-2">Name</th>
              <th className="p-2">Email</th>
              <th className="p-2">Role</th>
              <th className="p-2">Projects</th>
              <th className="p-2">Departments</th>
              <th className="p-2">Permissions</th>
              <th className="p-2">Active</th>
              <th className="p-2 text-right">Actions</th>
            </tr>
          </thead>
          <tbody>
            {users.map(u => {
              const selProjectIds = new Set(u.userProjects.map(up => up.project.id).filter(id => projectIds.has(id)))
              const selDeptIds = new Set(u.userDepartments.map(ud => ud.department.id).filter(id => departmentIds.has(id)))
              return (
                <tr key={u.id} className="border-t">
                  <td className="p-2">{u.fullName || '—'}</td>
                  <td className="p-2">{u.email}</td>
                  <td className="p-2">
                    <select value={u.role} onChange={e => updateUser(u, { role: e.target.value as any })} className="border rounded p-1">
                      <option value="CUSTOMER">Customer</option>
                      <option value="AGENT">Agent</option>
                      <option value="MANAGER">Manager</option>
                      <option value="ADMIN">Admin</option>
                    </select>
                  </td>
                  <td className="p-2">
                    <div className="max-w-xs grid grid-cols-1 gap-1">
                      {projects.map(p => (
                        <label key={p.id} className="flex items-center gap-2">
                          <input type="checkbox" checked={selProjectIds.has(p.id)} onChange={()=>{
                            const updated = new Set(selProjectIds)
                            if (updated.has(p.id)) updated.delete(p.id); else updated.add(p.id)
                            updateUser(u, { projectIds: Array.from(updated) })
                          }} />
                          <span className="truncate">{p.name}</span>
                        </label>
                      ))}
                    </div>
                  </td>
                  <td className="p-2">
                    <div className="max-w-xs grid grid-cols-1 gap-1">
                      {departments.map(d => (
                        <label key={d.id} className="flex items-center gap-2">
                          <input type="checkbox" checked={selDeptIds.has(d.id)} onChange={()=>{
                            const updated = new Set(selDeptIds)
                            if (updated.has(d.id)) updated.delete(d.id); else updated.add(d.id)
                            updateUser(u, { departmentIds: Array.from(updated) })
                          }} />
                          <span className="truncate">{d.name}</span>
                        </label>
                      ))}
                    </div>
                  </td>
                  <td className="p-2">
                    <div className="max-w-xs grid grid-cols-1 gap-1">
                      {ALL_PERMISSIONS.map(p => {
                        const sel = new Set((u.userPermissions || []).map(up => up.permission))
                        const checked = sel.has(p.key)
                        return (
                          <label key={p.key} className="flex items-center gap-2">
                            <input type="checkbox" checked={checked} onChange={()=>{
                              if (checked) sel.delete(p.key); else sel.add(p.key)
                              updateUser(u, { permissions: Array.from(sel) })
                            }} />
                            <span className="truncate">{p.label}</span>
                          </label>
                        )
                      })}
                    </div>
                  </td>
                  <td className="p-2">
                    <input type="checkbox" checked={u.isActive} onChange={()=> updateUser(u, { isActive: !u.isActive })} />
                  </td>
                  <td className="p-2 text-right">
                    <Button size="sm" disabled={savingId===u.id} variant="outline" onClick={()=>updateUser(u, { fullName: u.fullName || '' })}>
                      {savingId===u.id ? 'Saving...' : 'Save'}
                    </Button>
                  </td>
                </tr>
              )
            })}
          </tbody>
        </table>
      </div>
    </div>
  )
}

