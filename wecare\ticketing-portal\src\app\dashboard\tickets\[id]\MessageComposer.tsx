"use client"

import { useState } from 'react'

export default function MessageComposer({ ticketId }: { ticketId: string }) {
  const [content, setContent] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const submit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')
    try {
      const res = await fetch(`/api/tickets/${ticketId}/messages`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ content }),
      })
      if (!res.ok) {
        const data = await res.json().catch(() => ({}))
        setError(data.error || 'Failed to send message')
        return
      }
      setContent('')
      // Soft refresh messages by reloading the page (simple approach)
      window.location.reload()
    } catch (e) {
      setError('Unexpected error')
    } finally {
      setLoading(false)
    }
  }

  return (
    <form onSubmit={submit} className="space-y-3">
      {error && <div className="bg-red-50 text-red-700 border border-red-200 p-2 rounded">{error}</div>}
      <textarea
        value={content}
        onChange={(e) => setContent(e.target.value)}
        placeholder="Write a message..."
        className="w-full border rounded p-2 h-28"
        required
      />
      <div className="flex justify-end">
        <button type="submit" disabled={loading} className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded">
          {loading ? 'Sending...' : 'Send'}
        </button>
      </div>
    </form>
  )
}

