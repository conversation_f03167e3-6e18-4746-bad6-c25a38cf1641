import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { verifyToken } from '@/lib/auth-new'
import bcrypt from 'bcryptjs'

export const runtime = 'nodejs'

export async function GET(req: NextRequest) {
  const token = req.cookies.get('auth-token')?.value
  const user = token ? verifyToken(token) : null
  if (!user) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })

  const { searchParams } = new URL(req.url)
  const role = searchParams.get('role') as 'ADMIN' | 'MANAGER' | 'AGENT' | 'CUSTOMER' | null
  const projectId = searchParams.get('projectId')
  const departmentId = searchParams.get('departmentId')

  const where: any = { organizationId: user.organizationId }
  if (role) where.role = role

  // Filter by project membership if provided
  if (projectId) {
    where.userProjects = { some: { projectId } }
  }
  // Filter by department membership if provided
  if (departmentId) {
    where.userDepartments = { some: { departmentId } }
  }

  let users = await prisma.user.findMany({
    where,
    select: {
      id: true,
      email: true,
      fullName: true,
      role: true,
      isActive: true,
      userProjects: { select: { project: { select: { id: true, name: true } } } },
      userDepartments: { select: { department: { select: { id: true, name: true } } } },
    },
    orderBy: { createdAt: 'desc' },
    take: 200,
  })

  // Attach permissions per user without relying on relation selection (avoids stale client issues)
  const userIds = users.map(u => u.id)
  const upRows = userIds.length ? await prisma.userPermission.findMany({
    where: { userId: { in: userIds }, organizationId: user.organizationId },
    select: { userId: true, permission: true },
  }) : []
  const permsByUser = upRows.reduce((acc: Record<string, { permission: string }[]>, r) => {
    acc[r.userId] ||= []
    acc[r.userId].push({ permission: r.permission as any })
    return acc
  }, {})
  users = users.map(u => ({ ...u, userPermissions: permsByUser[u.id] || [] }))

  return NextResponse.json({ users })
}

export async function POST(req: NextRequest) {
  const token = req.cookies.get('auth-token')?.value
  const admin = token ? verifyToken(token) : null
  if (!admin) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  if (admin.role !== 'ADMIN') return NextResponse.json({ error: 'Forbidden' }, { status: 403 })

  const body = await req.json()
  const { email, fullName, password, role = 'CUSTOMER', projectIds = [], departmentIds = [] } = body || {}

  if (!email || !password) return NextResponse.json({ error: 'Email and password are required' }, { status: 400 })
  if (!['ADMIN', 'MANAGER', 'AGENT', 'CUSTOMER'].includes(role)) return NextResponse.json({ error: 'Invalid role' }, { status: 400 })

  try {
    const hashed = await bcrypt.hash(password, 12)
    const user = await prisma.user.create({
      data: {
        email,
        password: hashed,
        fullName: fullName || null,
        role,
        organizationId: admin.organizationId,
      },
    })

    if (Array.isArray(projectIds) && projectIds.length > 0) {
      const projects = await prisma.project.findMany({ where: { id: { in: projectIds }, organizationId: admin.organizationId }, select: { id: true } })
      for (const p of projects) {
        await prisma.userProject.upsert({
          where: { userId_projectId: { userId: user.id, projectId: p.id } },
          create: { userId: user.id, projectId: p.id },
          update: {},
        })
      }
    }

    if (Array.isArray(departmentIds) && departmentIds.length > 0) {
      const departments = await prisma.department.findMany({ where: { id: { in: departmentIds }, organizationId: admin.organizationId }, select: { id: true } })
      for (const d of departments) {
        await prisma.userDepartment.upsert({
          where: { userId_departmentId: { userId: user.id, departmentId: d.id } },
          create: { userId: user.id, departmentId: d.id },
          update: {},
        })
      }
    }

    const ip = req.headers.get('x-forwarded-for')?.split(',')[0] || null
    await prisma.userAuditLog.create({
      data: {
        organizationId: admin.organizationId,
        actorId: admin.id,
        targetUserId: user.id,
        action: 'USER_CREATE',
        after: { email, fullName: fullName || null, role, isActive: true, projectIds, departmentIds },
        ip: ip || undefined,
      },
    })

    return NextResponse.json({ user: { id: user.id, email: user.email, fullName: user.fullName, role: user.role } }, { status: 201 })
  } catch (e: any) {
    return NextResponse.json({ error: e?.message || 'Failed to create user' }, { status: 400 })
  }
}

