import { NextRequest, NextResponse } from 'next/server'
import { verifyToken } from '@/lib/auth-new'

export async function GET(req: NextRequest) {
  const token = req.cookies.get('auth-token')?.value
  if (!token) return NextResponse.json({ user: null }, { status: 401 })
  const user = verifyToken(token)
  if (!user) return NextResponse.json({ user: null }, { status: 401 })
  return NextResponse.json({ user })
}

