"use client"

import { useRouter } from 'next/navigation'

export default function LogoutButton({ className = '' }: { className?: string }) {
  const router = useRouter()
  const onClick = async () => {
    try {
      await fetch('/api/auth/logout', { method: 'POST' })
    } catch {}
    router.push('/auth/login')
    router.refresh()
  }
  return (
    <button onClick={onClick} className={className}>
      Sign out
    </button>
  )
}

