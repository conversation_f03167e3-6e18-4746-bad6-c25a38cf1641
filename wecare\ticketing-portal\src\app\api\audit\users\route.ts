import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { verifyToken } from '@/lib/auth-new'

export const runtime = 'nodejs'

export async function GET(req: NextRequest) {
  const token = req.cookies.get('auth-token')?.value
  const admin = token ? verifyToken(token) : null
  if (!admin) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  if (admin.role !== 'ADMIN') return NextResponse.json({ error: 'Forbidden' }, { status: 403 })

  const { searchParams } = new URL(req.url)
  const targetUserId = searchParams.get('targetUserId')
  const limit = Math.min(parseInt(searchParams.get('limit') || '50', 10) || 50, 200)

  const where: any = { organizationId: admin.organizationId }
  if (targetUserId) where.targetUserId = targetUserId

  const logs = await prisma.userAuditLog.findMany({
    where,
    orderBy: { createdAt: 'desc' },
    take: limit,
    select: {
      id: true,
      action: true,
      before: true,
      after: true,
      ip: true,
      createdAt: true,
      actor: { select: { id: true, fullName: true, email: true } },
      targetUser: { select: { id: true, fullName: true, email: true } },
    },
  })

  return NextResponse.json({ logs })
}

