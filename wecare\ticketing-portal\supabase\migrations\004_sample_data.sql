-- Insert sample organizations
INSERT INTO organizations (id, name, domain) VALUES
    ('550e8400-e29b-41d4-a716-************', 'Acme Corporation', 'acme.com'),
    ('550e8400-e29b-41d4-a716-************', 'TechStart Inc', 'techstart.io');

-- Note: In a real application, profiles will be created automatically when users sign up
-- This is just for reference of the expected data structure

-- Sample ticket categories for reference
CREATE TABLE IF NOT EXISTS ticket_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

INSERT INTO ticket_categories (name, description, organization_id) VALUES
    ('Technical Support', 'Technical issues and troubleshooting', '550e8400-e29b-41d4-a716-************'),
    ('Billing', 'Billing and payment related inquiries', '550e8400-e29b-41d4-a716-************'),
    ('Feature Request', 'Requests for new features or enhancements', '550e8400-e29b-41d4-a716-************'),
    ('Bug Report', 'Software bugs and issues', '550e8400-e29b-41d4-a716-************'),
    ('General Inquiry', 'General questions and information requests', '550e8400-e29b-41d4-a716-************');

-- Create a view for ticket statistics
CREATE OR REPLACE VIEW ticket_stats AS
SELECT 
    t.organization_id,
    COUNT(*) as total_tickets,
    COUNT(*) FILTER (WHERE status = 'open') as open_tickets,
    COUNT(*) FILTER (WHERE status = 'in_progress') as in_progress_tickets,
    COUNT(*) FILTER (WHERE status = 'resolved') as resolved_tickets,
    COUNT(*) FILTER (WHERE status = 'closed') as closed_tickets,
    COUNT(*) FILTER (WHERE priority = 'urgent') as urgent_tickets,
    COUNT(*) FILTER (WHERE priority = 'high') as high_priority_tickets,
    AVG(
        CASE 
            WHEN resolved_at IS NOT NULL 
            THEN EXTRACT(EPOCH FROM (resolved_at - created_at))/3600 
        END
    ) as avg_resolution_time_hours,
    AVG(
        CASE 
            WHEN first_response_at IS NOT NULL 
            THEN EXTRACT(EPOCH FROM (first_response_at - created_at))/3600 
        END
    ) as avg_first_response_time_hours
FROM tickets t
GROUP BY t.organization_id;

-- Create a view for agent performance
CREATE OR REPLACE VIEW agent_performance AS
SELECT 
    p.id as agent_id,
    p.full_name as agent_name,
    p.organization_id,
    COUNT(t.id) as assigned_tickets,
    COUNT(t.id) FILTER (WHERE t.status = 'resolved') as resolved_tickets,
    COUNT(t.id) FILTER (WHERE t.status = 'closed') as closed_tickets,
    AVG(
        CASE 
            WHEN t.resolved_at IS NOT NULL 
            THEN EXTRACT(EPOCH FROM (t.resolved_at - t.created_at))/3600 
        END
    ) as avg_resolution_time_hours,
    COUNT(tm.id) as total_responses,
    AVG(
        CASE 
            WHEN t.first_response_at IS NOT NULL 
            THEN EXTRACT(EPOCH FROM (t.first_response_at - t.created_at))/3600 
        END
    ) as avg_first_response_time_hours
FROM profiles p
LEFT JOIN tickets t ON p.id = t.assigned_agent_id
LEFT JOIN ticket_messages tm ON t.id = tm.ticket_id AND tm.author_id = p.id
WHERE p.role IN ('agent', 'admin')
GROUP BY p.id, p.full_name, p.organization_id;

-- Create indexes for the views
CREATE INDEX IF NOT EXISTS idx_tickets_organization_status ON tickets(organization_id, status);
CREATE INDEX IF NOT EXISTS idx_tickets_organization_priority ON tickets(organization_id, priority);
CREATE INDEX IF NOT EXISTS idx_tickets_resolved_at ON tickets(resolved_at) WHERE resolved_at IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_tickets_first_response_at ON tickets(first_response_at) WHERE first_response_at IS NOT NULL;
