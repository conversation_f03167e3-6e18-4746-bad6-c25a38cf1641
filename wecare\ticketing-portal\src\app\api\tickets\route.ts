import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { verifyToken } from '@/lib/auth-new'
import { writeFile, mkdir } from 'fs/promises'
import path from 'path'
import type { Priority } from '@prisma/client'
export const runtime = 'nodejs'


import { getUserPermissions, hasPermission } from '@/lib/permissions'

export async function GET(req: NextRequest) {
  const token = req.cookies.get('auth-token')?.value
  const user = token ? verifyToken(token) : null
  if (!user) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })

  // Admin can see all tickets within org
  if (user.role !== 'ADMIN') {
    const perms = await getUserPermissions({ id: user.id, role: user.role, organizationId: user.organizationId })
    if (!hasPermission(perms, 'TICKETS_VIEW')) {
      return NextResponse.json({ error: 'Forbidden: missing permission TICKETS_VIEW' }, { status: 403 })
    }
  }

  // Visibility: must belong to both project AND department
  const memberships = await prisma.userProject.findMany({ where: { userId: user.id }, select: { projectId: true } })
  const projectIds = memberships.map(m => m.projectId)
  const deptLinks = await prisma.userDepartment.findMany({ where: { userId: user.id }, select: { departmentId: true } })
  const departmentIds = deptLinks.map(d => d.departmentId)
  if (user.role !== 'ADMIN' && (projectIds.length === 0 || departmentIds.length === 0)) {
    return NextResponse.json({ tickets: [] })
  }

  const tickets = await prisma.ticket.findMany({
    where: {
      organizationId: user.organizationId,
      ...(user.role === 'ADMIN' ? {} : { projectId: { in: projectIds }, departmentId: { in: departmentIds } }),
    },
    include: { createdBy: { select: { fullName: true, email: true } }, assignedTo: { select: { fullName: true, email: true } } },
    orderBy: { createdAt: 'desc' },
    take: 50,
  })
  return NextResponse.json({ tickets })
}

export async function POST(req: NextRequest) {
  const token = req.cookies.get('auth-token')?.value
  const user = token ? verifyToken(token) : null
  if (!user) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })

  const contentType = req.headers.get('content-type') || ''
  let title = ''
  let description = ''
  let priority: Priority = 'MEDIUM'
  let category: string | undefined
  let projectId: string | undefined
  let departmentId: string | undefined
  let files: File[] = []

  if (contentType.includes('multipart/form-data')) {
    const form = await req.formData()
    title = String(form.get('title') || '')
    description = String(form.get('description') || '')
    {
      const p = String(form.get('priority') || 'MEDIUM').toUpperCase()
      priority = ((['LOW','MEDIUM','HIGH','URGENT'] as const).includes(p as any) ? p : 'MEDIUM') as Priority
    }
    category = form.get('category') ? String(form.get('category')) : undefined
    projectId = form.get('projectId') ? String(form.get('projectId')) : undefined
    departmentId = form.get('departmentId') ? String(form.get('departmentId')) : undefined
    const images = form.getAll('images') as unknown[]
    files = images.filter((f): f is File => typeof (f as any)?.arrayBuffer === 'function')
  } else {
    const body = await req.json()
    title = body?.title || ''
    description = body?.description || ''
    {
      const p = String(body?.priority || 'MEDIUM').toUpperCase()
      priority = ((['LOW','MEDIUM','HIGH','URGENT'] as const).includes(p as any) ? p : 'MEDIUM') as Priority
    }
    category = body?.category
    projectId = body?.projectId
    departmentId = body?.departmentId
  }

  if (!title) return NextResponse.json({ error: 'Title is required' }, { status: 400 })
  if (!projectId) return NextResponse.json({ error: 'Project is required' }, { status: 400 })
  if (!departmentId) return NextResponse.json({ error: 'Department is required' }, { status: 400 })

  // Enforce membership in project
  const membership = await prisma.userProject.findUnique({ where: { userId_projectId: { userId: user.id, projectId } } })
  if (!membership) return NextResponse.json({ error: 'Forbidden: not a member of the selected project' }, { status: 403 })

  // Validate department belongs to same organization AND the user belongs to that department
  const dept = await prisma.department.findFirst({ where: { id: departmentId, organizationId: user.organizationId }, select: { id: true } })
  if (!dept) return NextResponse.json({ error: 'Invalid department' }, { status: 400 })
  const deptMember = await prisma.userDepartment.findUnique({ where: { userId_departmentId: { userId: user.id, departmentId } } })
  if (!deptMember && user.role !== 'ADMIN') return NextResponse.json({ error: 'Forbidden: not a member of the selected department' }, { status: 403 })

  // generate simple ticket number (for demo)
  const timestamp = new Date().toISOString().replace(/[-:TZ.]/g, '').slice(0, 14)
  const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0')
  const ticketNumber = `TKT-${timestamp}-${random}`

  const ticket = await prisma.ticket.create({
    data: {
      ticketNumber,
      title,
      description,
      priority,
      category,
      organizationId: user.organizationId,
      createdById: user.id,
      projectId,
      departmentId,
    },
  })

  // Save image attachments if any (limit 3)
  if (files && files.length) {
    const uploadDir = path.join(process.cwd(), 'public', 'uploads')
    await mkdir(uploadDir, { recursive: true })
    const toSave = files.slice(0, 3)
    for (const file of toSave) {
      // basic validation
      // @ts-ignore - next File has type + name at runtime
      const mime = (file as any).type || 'application/octet-stream'
      if (!mime.startsWith('image/')) continue
      const arrayBuffer = await file.arrayBuffer()
      const buffer = Buffer.from(arrayBuffer)
      // @ts-ignore
      const originalName: string = (file as any).name || 'image'
      const safeName = originalName.replace(/[^a-zA-Z0-9._-]/g, '_')
      const basename = `${Date.now()}-${Math.random().toString(36).slice(2,8)}-${safeName}`
      const filePath = path.join(uploadDir, basename)
      await writeFile(filePath, buffer)
      const url = `/uploads/${basename}`
      await prisma.attachment.create({
        data: {
          filename: basename,
          originalName,
          mimeType: mime,
          size: buffer.length,
          url,
          ticketId: ticket.id,
        },
      })
    }
  }

  // Audit log (internal message)
  await prisma.ticketMessage.create({
    data: {
      content: `[AUDIT] Ticket created by ${user.fullName || user.email}. Priority=${priority}${category ? `, Category=${category}` : ''}`,
      isInternal: true,
      ticketId: ticket.id,
      authorId: user.id,
    },
  })

  return NextResponse.json({ ticket }, { status: 201 })
}
