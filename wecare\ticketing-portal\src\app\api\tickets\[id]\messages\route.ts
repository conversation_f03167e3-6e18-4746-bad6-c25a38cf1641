import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { verifyToken } from '@/lib/auth-new'

export async function POST(req: NextRequest, context: { params: Promise<{ id: string }> }) {
  const token = req.cookies.get('auth-token')?.value
  const user = token ? verifyToken(token) : null
  if (!user) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })

  const { params } = context
  const { id } = await params
  const ticket = await prisma.ticket.findUnique({ where: { id } })
  if (!ticket || ticket.organizationId !== user.organizationId) {
    return NextResponse.json({ error: 'Not found' }, { status: 404 })
  }
  if (ticket.projectId) {
    const membership = await prisma.userProject.findUnique({ where: { userId_projectId: { userId: user.id, projectId: ticket.projectId } } })
    if (!membership) return NextResponse.json({ error: 'Not found' }, { status: 404 })
  }

  const { content, isInternal = false } = await req.json()
  if (!content || typeof content !== 'string') {
    return NextResponse.json({ error: 'Content is required' }, { status: 400 })
  }

  if (isInternal && user.role === 'CUSTOMER') {
    return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
  }

  const message = await prisma.ticketMessage.create({
    data: {
      content,
      isInternal,
      ticketId: ticket.id,
      authorId: user.id,
    },
    include: { author: { select: { fullName: true, email: true } } },
  })

  return NextResponse.json({ message }, { status: 201 })
}

