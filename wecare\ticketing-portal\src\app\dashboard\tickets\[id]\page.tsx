import { cookies } from 'next/headers'
import Link from 'next/link'
import { prisma } from '@/lib/prisma'
import { verifyToken } from '@/lib/auth-new'
import MessageComposer from './MessageComposer'
import TicketControls from './TicketControls'

export default async function TicketDetailPage({ params }: { params: { id: string } }) {
  const cookieStore = await cookies()
  const token = cookieStore.get('auth-token')?.value
  const user = token ? verifyToken(token) : null
  if (!user) return <div className="p-6">Unauthorized</div>

  const ticket = await prisma.ticket.findUnique({
    where: { id: params.id },
    include: {
      createdBy: { select: { fullName: true, email: true } },
      assignedTo: { select: { id: true, fullName: true, email: true } },
      project: { select: { id: true, name: true } },
      department: { select: { id: true, name: true } },
      messages: { include: { author: { select: { fullName: true, email: true } } }, orderBy: { createdAt: 'asc' } },
      attachments: true,
    },
  })
  if (!ticket || ticket.organizationId !== user.organizationId) {
    return <div className="p-6">Not found</div>
  }
  if (ticket.project?.id) {
    const membership = await prisma.userProject.findUnique({ where: { userId_projectId: { userId: user.id, projectId: ticket.project.id } } })
    if (!membership && user.role !== 'ADMIN') return <div className="p-6">Not found</div>
  }
  if (ticket.department?.id) {
    const deptMember = await prisma.userDepartment.findUnique({ where: { userId_departmentId: { userId: user.id, departmentId: ticket.department.id } } })
    if (!deptMember && user.role !== 'ADMIN') return <div className="p-6">Not found</div>
  }

  const conversation = ticket.messages.filter(m => !m.isInternal)
  const auditLog = ticket.messages.filter(m => m.isInternal && m.content.startsWith('[AUDIT]'))

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">{ticket.ticketNumber}</h1>
          <p className="text-gray-600">{ticket.title}</p>
        </div>
        <Link href="/dashboard/tickets" className="text-blue-600 hover:underline">Back to Tickets</Link>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          <div className="bg-white rounded-lg shadow">
            <div className="p-4 border-b"><h2 className="font-semibold">Conversation</h2></div>
            <div className="p-4 space-y-4">
              {conversation.map(m => (
                <div key={m.id} className="p-3 bg-gray-50 rounded">
                  <div className="text-sm text-gray-500">{m.author.fullName || m.author.email} • {new Date(m.createdAt).toLocaleString()}</div>
                  <div className="mt-1 text-sm text-gray-800 whitespace-pre-wrap">{m.content}</div>
                </div>
              ))}
              {conversation.length === 0 && (
                <div className="text-sm text-gray-500">No messages yet.</div>
              )}
            </div>
            <div className="p-4 border-t">
              <MessageComposer ticketId={ticket.id} />
            </div>
          </div>
        </div>

        <div className="space-y-6">
          <div className="bg-white rounded-lg shadow p-4">
            <h3 className="font-semibold mb-3">Details</h3>
            <div className="text-sm text-gray-600 space-y-1">
              <div><strong>Status:</strong> {ticket.status}</div>
              <div><strong>Priority:</strong> {ticket.priority}</div>
              {ticket.project && <div><strong>Project:</strong> {ticket.project.name}</div>}
              {ticket.department && <div><strong>Department:</strong> {ticket.department.name}</div>}
              {ticket.category && <div><strong>Category:</strong> {ticket.category}</div>}
              <div><strong>Created:</strong> {new Date(ticket.createdAt).toLocaleString()}</div>
              {ticket.assignedTo && (
                <div><strong>Assigned To:</strong> {ticket.assignedTo.fullName || ticket.assignedTo.email}</div>
              )}
            </div>
          </div>
          <div className="bg-white rounded-lg shadow p-4">
            <h3 className="font-semibold mb-3">Update</h3>
            <TicketControls ticketId={ticket.id} role={user.role} initial={{ status: ticket.status, priority: ticket.priority, departmentId: ticket.department?.id || '', assignedToId: ticket.assignedTo?.id || '', projectId: ticket.project?.id || '' }} />
          </div>
          {ticket.attachments.length > 0 && (
            <div className="bg-white rounded-lg shadow p-4">
              <h3 className="font-semibold mb-3">Attachments</h3>
              <div className="grid grid-cols-2 gap-3">
                {ticket.attachments.map(att => (
                  <a key={att.id} href={att.url} target="_blank" className="block">
                    <img src={att.url} alt={att.originalName} className="w-full h-32 object-cover rounded border" />
                  </a>
                ))}
              </div>
            </div>
          )}
          <div className="bg-white rounded-lg shadow p-4">
            <h3 className="font-semibold mb-3">Lifecycle</h3>
            <div className="space-y-2">
              {auditLog.map(m => (
                <div key={m.id} className="text-sm text-gray-600">
                  <div>{m.content.replace(/^\[AUDIT\]\s*/, '')}</div>
                  <div className="text-xs text-gray-400">{new Date(m.createdAt).toLocaleString()} • {m.author.fullName || m.author.email}</div>
                </div>
              ))}
              {auditLog.length === 0 && <div className="text-sm text-gray-500">No lifecycle entries yet.</div>}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

