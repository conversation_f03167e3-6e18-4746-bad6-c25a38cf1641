"use client"

import { useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'

type Department = { id: string; name: string; description?: string | null }

export default function DepartmentManager() {
  const [items, setItems] = useState<Department[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [savingId, setSavingId] = useState<string | null>(null)
  const [newName, setNewName] = useState('')
  const [newDesc, setNewDesc] = useState('')

  const load = async () => {
    setLoading(true)
    setError('')
    try {
      const res = await fetch('/api/departments')
      const data = await res.json()
      if (!res.ok) throw new Error(data.error || 'Failed to load departments')
      setItems(data.departments || [])
    } catch (e: any) {
      setError(e?.message || 'Failed to load')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => { load() }, [])

  const create = async () => {
    if (!newName.trim()) return
    setSavingId('new')
    setError('')
    try {
      const res = await fetch('/api/departments', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: newName.trim(), description: newDesc.trim() || null }),
      })
      const data = await res.json()
      if (!res.ok) throw new Error(data.error || 'Failed to create')
      setNewName(''); setNewDesc('')
      await load()
    } catch (e: any) {
      setError(e?.message || 'Failed to create department')
    } finally {
      setSavingId(null)
    }
  }

  const save = async (id: string, name: string, description: string) => {
    setSavingId(id)
    setError('')
    try {
      const res = await fetch(`/api/departments/${id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name, description }),
      })
      const data = await res.json()
      if (!res.ok) throw new Error(data.error || 'Failed to update')
      await load()
    } catch (e: any) {
      setError(e?.message || 'Failed to update department')
    } finally {
      setSavingId(null)
    }
  }

  const remove = async (id: string) => {
    if (!confirm('Delete this department? This cannot be undone.')) return
    setSavingId(id)
    setError('')
    try {
      const res = await fetch(`/api/departments/${id}`, { method: 'DELETE' })
      const data = await res.json().catch(() => ({ success: res.ok }))
      if (!res.ok) throw new Error(data.error || 'Failed to delete')
      await load()
    } catch (e: any) {
      setError(e?.message || 'Failed to delete department')
    } finally {
      setSavingId(null)
    }
  }

  if (loading) return <div className="p-4">Loading departments...</div>
  return (
    <div className="bg-white rounded-lg shadow">
      <div className="p-4 border-b flex items-center justify-between">
        <h2 className="font-semibold">Departments</h2>
        <Button variant="outline" onClick={load}>Refresh</Button>
      </div>
      <div className="p-4 space-y-4">
        {error && <div className="bg-red-50 text-red-700 border border-red-200 p-2 rounded">{error}</div>}
        <div className="grid gap-2 sm:grid-cols-[240px_1fr_auto] items-start">
          <Input placeholder="New department name" value={newName} onChange={e=>setNewName(e.target.value)} />
          <Input placeholder="Description (optional)" value={newDesc} onChange={e=>setNewDesc(e.target.value)} />
          <Button onClick={create} disabled={savingId==='new' || !newName.trim()}>{savingId==='new' ? 'Adding...' : 'Add'}</Button>
        </div>
        <div className="divide-y">
          {items.map((d) => (
            <Row key={d.id} d={d} onSave={save} onDelete={remove} saving={savingId===d.id} />
          ))}
          {items.length === 0 && <div className="text-sm text-gray-500">No departments found.</div>}
        </div>
      </div>
    </div>
  )
}

function Row({ d, onSave, onDelete, saving }: { d: Department, onSave: (id:string,n:string,desc:string)=>void, onDelete: (id:string)=>void, saving: boolean }) {
  const [name, setName] = useState(d.name)
  const [desc, setDesc] = useState(d.description || '')
  const dirty = name !== d.name || (desc || '') !== (d.description || '')

  return (
    <div className="py-3 grid gap-2 sm:grid-cols-[240px_1fr_auto] items-center">
      <Input value={name} onChange={e=>setName(e.target.value)} />
      <Input value={desc} onChange={e=>setDesc(e.target.value)} />
      <div className="flex gap-2 justify-end">
        <Button size="sm" variant="outline" onClick={()=>onDelete(d.id)} disabled={saving}>Delete</Button>
        <Button size="sm" onClick={()=>onSave(d.id, name, desc)} disabled={saving || !dirty}>{saving ? 'Saving...' : 'Save'}</Button>
      </div>
    </div>
  )
}

