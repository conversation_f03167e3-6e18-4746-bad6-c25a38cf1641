import { cookies } from 'next/headers'
import { redirect } from 'next/navigation'
import { prisma } from '@/lib/prisma'
import { verifyToken } from '@/lib/auth-new'
import { getUserPermissions, hasPermission } from '@/lib/permissions'


function hours(n: number) {
  return Math.round(n * 10) / 10
}

export default async function DashboardPage() {
  const cookieStore = await cookies()
  const token = cookieStore.get('auth-token')?.value
  const user = token ? verifyToken(token) : null
  if (!user) redirect('/auth/login')

  // Permission: non-admins must have TICKETS_VIEW to see dashboard data
  if (user.role !== 'ADMIN') {
    const perms = await getUserPermissions({ id: user.id, role: user.role, organizationId: user.organizationId })
    if (!hasPermission(perms, 'TICKETS_VIEW')) {
      return (
        <div className="min-h-screen bg-gray-50 p-6">
          <div className="max-w-3xl mx-auto bg-white rounded-lg shadow p-6">Forbidden</div>
        </div>
      )
    }
  }

  // Scope by both projects and departments for non-admins
  const memberships = await prisma.userProject.findMany({ where: { userId: user.id }, select: { projectId: true } })
  const projectIds = memberships.map(m => m.projectId)
  const deptLinks = await prisma.userDepartment.findMany({ where: { userId: user.id }, select: { departmentId: true } })
  const departmentIds = deptLinks.map(d => d.departmentId)

  const baseWhere: any = user.role === 'ADMIN'
    ? { organizationId: user.organizationId }
    : {
        organizationId: user.organizationId,
        projectId: { in: projectIds.length ? projectIds : ['__none__'] },
        departmentId: { in: departmentIds.length ? departmentIds : ['__none__'] },
      }

  const [total, open, inProgress, closed, cancelled, resolved, recent] = await Promise.all([
    prisma.ticket.count({ where: baseWhere }),
    prisma.ticket.count({ where: { ...baseWhere, status: 'OPEN' } }),
    prisma.ticket.count({ where: { ...baseWhere, status: 'IN_PROGRESS' } }),
    prisma.ticket.count({ where: { ...baseWhere, status: 'CLOSED' } }),
    prisma.ticket.count({ where: { ...baseWhere, status: 'CANCELLED' } }),
    prisma.ticket.count({ where: { ...baseWhere, status: 'RESOLVED' } }),
    prisma.ticket.findMany({
      where: baseWhere,
      include: { createdBy: { select: { fullName: true, email: true } } },
      orderBy: { createdAt: 'desc' },
      take: 5,
    }),
  ])

  // TAT metrics
  const [resolvedSamples, openSamples] = await Promise.all([
    prisma.ticket.findMany({
      where: { ...baseWhere, OR: [{ status: 'RESOLVED' }, { status: 'CLOSED' }], resolvedAt: { not: null } },
      select: { createdAt: true, resolvedAt: true },
      orderBy: { resolvedAt: 'desc' },
      take: 200,
    }),
    prisma.ticket.findMany({
      where: { ...baseWhere, status: { in: ['OPEN', 'IN_PROGRESS', 'WAITING_FOR_CUSTOMER', 'WAITING_FOR_AGENT'] } },
      select: { createdAt: true },
      orderBy: { createdAt: 'desc' },
      take: 200,
    }),
  ])

  const resolvedHours: number[] = resolvedSamples
    .map((t) => ((t.resolvedAt!.getTime() - t.createdAt.getTime()) / 36e5))
    .filter((n: number) => n >= 0)
    .sort((a: number, b: number) => a - b)
  const avgResolvedH = resolvedHours.length ? hours(resolvedHours.reduce((a: number, b: number) => a + b, 0) / resolvedHours.length) : 0
  const medResolvedH = resolvedHours.length ? hours(resolvedHours[Math.floor(resolvedHours.length / 2)]) : 0

  const now = Date.now()
  const openAgesH: number[] = openSamples
    .map((t: { createdAt: Date }) => ((now - t.createdAt.getTime()) / 36e5))
    .filter((n: number) => n >= 0)
  const avgOpenAgeH = openAgesH.length ? hours(openAgesH.reduce((a: number, b: number) => a + b, 0) / openAgesH.length) : 0

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        <div className="bg-white rounded-lg shadow p-6">
          <h1 className="text-2xl font-bold text-gray-900">Welcome back, {user.fullName || user.email}</h1>
          <p className="mt-1 text-sm text-gray-600">Here is the latest on your tickets.</p>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
          {[{label:'Open',value:open},{label:'WIP',value:inProgress},{label:'Closed',value:closed},{label:'Discarded',value:cancelled},{label:'Total',value:total}].map(s=> (
            <div key={s.label} className="bg-white rounded-lg shadow p-4">
              <div className="text-sm text-gray-500">{s.label}</div>
              <div className="text-2xl font-bold">{s.value}</div>
            </div>
          ))}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-white rounded-lg shadow p-4">
            <div className="text-sm text-gray-500">TAT (Resolved/Closed)</div>
            <div className="text-2xl font-bold">{avgResolvedH}h</div>
            <div className="text-xs text-gray-500">Median: {medResolvedH}h</div>
          </div>
          <div className="bg-white rounded-lg shadow p-4">
            <div className="text-sm text-gray-500">Average age (Open/WIP)</div>
            <div className="text-2xl font-bold">{avgOpenAgeH}h</div>
            <div className="text-xs text-gray-500">Active: {openSamples.length}</div>
          </div>
          <div className="bg-white rounded-lg shadow p-4">
            <div className="text-sm text-gray-500">Resolved (count)</div>
            <div className="text-2xl font-bold">{resolved}</div>
            <div className="text-xs text-gray-500">Last {resolvedSamples.length} used for TAT</div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow">
          <div className="p-4 border-b"><h2 className="font-semibold">Recent Tickets</h2></div>
          <ul className="divide-y">
            {recent.length === 0 ? (
              <li className="p-4 text-sm text-gray-500">No recent tickets in your departments.</li>
            ) : recent.map((t: any) => (
              <li key={t.id} className="p-4 flex items-center justify-between">
                <div>
                  <div className="font-medium text-blue-700">{t.ticketNumber}</div>
                  <div className="text-sm text-gray-600">{t.title}</div>
                  <div className="text-xs text-gray-500">{t.createdBy.fullName || t.createdBy.email}</div>
                </div>
                <span className="text-xs rounded-full px-2 py-1 bg-gray-100 text-gray-700">{t.status}</span>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  )
}
