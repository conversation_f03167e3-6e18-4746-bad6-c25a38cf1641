"use client"

import { useEffect, useState, useRef } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'

type Project = { id: string; name: string }
type Department = { id: string; name: string }

const CATEGORIES = [
  'Technical Support',
  'Billing',
  'Account',
  'Feature Request',
  'Data Management',
  'Other',
]

export default function NewTicketPage() {
  const router = useRouter()
  const [title, setTitle] = useState('')
  const [description, setDescription] = useState('')
  const [priority, setPriority] = useState('MEDIUM')
  const [category, setCategory] = useState('Technical Support')
  const [projects, setProjects] = useState<Project[]>([])
  const [projectId, setProjectId] = useState('')
  const [departments, setDepartments] = useState<Department[]>([])
  const [departmentId, setDepartmentId] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const fileInputRef = useRef<HTMLInputElement | null>(null)

  useEffect(() => {
    (async () => {
      try {
        const [prjRes, deptRes] = await Promise.all([
          fetch('/api/projects'),
          fetch('/api/departments?my=1'),
        ])
        const prjData = await prjRes.json()
        const deptData = await deptRes.json()
        if (prjRes.ok) {
          setProjects(prjData.projects || [])
          if ((prjData.projects || []).length > 0) setProjectId(prjData.projects[0].id)
        } else {
          setError(prjData.error || 'Failed to load projects')
        }
        if (deptRes.ok) {
          setDepartments(deptData.departments || [])
          if ((deptData.departments || []).length > 0) setDepartmentId(deptData.departments[0].id)
        } else if (!error) {
          setError(deptData.error || 'Failed to load departments')
        }
      } catch {
        setError('Failed to load projects/departments')
      }
    })()
  }, [])

  const submit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')
    try {
      if (!projectId) {
        setError('Please select a project')
        setLoading(false)
        return
      }
      if (!departmentId) {
        setError('Please select a department')
        setLoading(false)
        return
      }
      const fd = new FormData()
      fd.append('title', title)
      fd.append('description', description)
      fd.append('priority', priority)
      fd.append('category', category)
      fd.append('projectId', projectId)
      fd.append('departmentId', departmentId)
      const files = fileInputRef.current?.files
      if (files) {
        const count = Math.min(files.length, 3)
        for (let i = 0; i < count; i++) {
          fd.append('images', files[i])
        }
      }
      const res = await fetch('/api/tickets', {
        method: 'POST',
        body: fd,
      })
      const data = await res.json()
      if (!res.ok) {
        setError(data.error || 'Failed to create ticket')
        return
      }
      router.push('/dashboard/tickets')
      router.refresh()
    } catch (e) {
      setError('Unexpected error')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="max-w-2xl mx-auto space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Create New Ticket</h1>
        <p className="mt-1 text-sm text-gray-600">Submit a new support request.</p>
      </div>
      <Card>
        <CardHeader>
          <CardTitle>Ticket Details</CardTitle>
          <CardDescription>Provide details to help us resolve your issue.</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={submit} className="space-y-4">
            {error && <div className="bg-red-50 text-red-700 border border-red-200 p-3 rounded">{error}</div>}
            <div>
              <label className="block text-sm font-medium text-gray-700">Project</label>
              <select value={projectId} onChange={e=>setProjectId(e.target.value)} className="mt-1 w-full border rounded p-2" required>
                {projects.map(p => <option key={p.id} value={p.id}>{p.name}</option>)}
              </select>
              {projects.length === 0 && <p className="text-xs text-gray-500 mt-1">No projects available. Ask an admin to assign you to a project.</p>}
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Department</label>
              <select value={departmentId} onChange={e=>setDepartmentId(e.target.value)} className="mt-1 w-full border rounded p-2" required>
                {departments.map(d => <option key={d.id} value={d.id}>{d.name}</option>)}
              </select>
              {departments.length === 0 && <p className="text-xs text-gray-500 mt-1">No departments configured. Ask an admin to add departments.</p>}
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Title</label>
              <Input value={title} onChange={e=>setTitle(e.target.value)} required className="mt-1" />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Description</label>
              <textarea value={description} onChange={e=>setDescription(e.target.value)} required className="mt-1 w-full border rounded p-2 h-32" />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Priority</label>
              <select value={priority} onChange={e=>setPriority(e.target.value)} className="mt-1 w-full border rounded p-2">
                <option>LOW</option>
                <option>MEDIUM</option>
                <option>HIGH</option>
                <option>URGENT</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Category</label>
              <select value={category} onChange={e=>setCategory(e.target.value)} className="mt-1 w-full border rounded p-2">
                {CATEGORIES.map(c => <option key={c} value={c}>{c}</option>)}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Images (up to 3)</label>
              <input ref={fileInputRef} type="file" accept="image/*" multiple className="mt-1 w-full border rounded p-2" name="images" onChange={(e)=>{
                const files = e.currentTarget.files
                if (files && files.length > 3) {
                  e.currentTarget.value = ''
                  alert('Please select up to 3 images')
                }
              }} />
              <p className="text-xs text-gray-500 mt-1">PNG, JPG, or GIF. Max 3 images.</p>
            </div>
            <div className="flex gap-2">
              <Button type="submit" disabled={loading || projects.length === 0}>{loading ? 'Creating...' : 'Create Ticket'}</Button>
              <Button type="button" variant="outline" onClick={()=>router.back()}>Cancel</Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
