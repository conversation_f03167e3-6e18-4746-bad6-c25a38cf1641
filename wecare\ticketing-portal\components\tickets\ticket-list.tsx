'use client'

import Link from 'next/link'
import { formatDate, getStatusColor, getPriorityColor } from '@/lib/utils'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline'

interface TicketListProps {
  tickets: any[]
  profile: any
  totalCount: number
  currentPage: number
}

export function TicketList({ tickets, profile, totalCount, currentPage }: TicketListProps) {
  const itemsPerPage = 20
  const totalPages = Math.ceil(totalCount / itemsPerPage)

  if (tickets.length === 0) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <div className="text-gray-500">
            <svg
              className="mx-auto h-12 w-12 text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              aria-hidden="true"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">No tickets found</h3>
            <p className="mt-1 text-sm text-gray-500">
              Get started by creating a new ticket.
            </p>
            <div className="mt-6">
              <Link href="/dashboard/tickets/new">
                <Button>Create New Ticket</Button>
              </Link>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      {/* Tickets */}
      <div className="space-y-3">
        {tickets.map((ticket) => (
          <Card key={ticket.id} className="hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-3">
                    <Link
                      href={`/dashboard/tickets/${ticket.id}`}
                      className="text-lg font-medium text-blue-600 hover:text-blue-500"
                    >
                      {ticket.ticket_number}
                    </Link>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(ticket.status)}`}>
                      {ticket.status.replace('_', ' ')}
                    </span>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(ticket.priority)}`}>
                      {ticket.priority}
                    </span>
                  </div>
                  <h3 className="mt-1 text-sm font-medium text-gray-900 truncate">
                    {ticket.title}
                  </h3>
                  <p className="mt-1 text-sm text-gray-600 line-clamp-2">
                    {ticket.description}
                  </p>
                  <div className="mt-2 flex items-center space-x-4 text-xs text-gray-500">
                    <span>Created {formatDate(ticket.created_at)}</span>
                    {profile.role !== 'customer' && (
                      <span>
                        Customer: {ticket.customer?.full_name || ticket.customer?.email}
                      </span>
                    )}
                    {ticket.assigned_agent && (
                      <span>
                        Assigned to: {ticket.assigned_agent.full_name || ticket.assigned_agent.email}
                      </span>
                    )}
                  </div>
                </div>
                <div className="flex-shrink-0">
                  <Link href={`/dashboard/tickets/${ticket.id}`}>
                    <Button variant="outline" size="sm">
                      View
                    </Button>
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6">
          <div className="flex flex-1 justify-between sm:hidden">
            <Link
              href={`?page=${currentPage - 1}`}
              aria-disabled={currentPage <= 1}
              className={`inline-flex items-center justify-center rounded-md border border-gray-300 bg-white text-gray-900 hover:bg-gray-50 text-sm h-10 px-4 py-2 ${currentPage <= 1 ? 'pointer-events-none opacity-50' : ''}`}
            >
              Previous
            </Link>
            <Link
              href={`?page=${currentPage + 1}`}
              aria-disabled={currentPage >= totalPages}
              className={`inline-flex items-center justify-center rounded-md border border-gray-300 bg-white text-gray-900 hover:bg-gray-50 text-sm h-10 px-4 py-2 ${currentPage >= totalPages ? 'pointer-events-none opacity-50' : ''}`}
            >
              Next
            </Link>
          </div>
          <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700">
                Showing{' '}
                <span className="font-medium">{(currentPage - 1) * itemsPerPage + 1}</span>
                {' '}to{' '}
                <span className="font-medium">
                  {Math.min(currentPage * itemsPerPage, totalCount)}
                </span>
                {' '}of{' '}
                <span className="font-medium">{totalCount}</span>
                {' '}results
              </p>
            </div>
            <div>
              <nav className="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                <Link
                  href={`?page=${currentPage - 1}`}
                  aria-disabled={currentPage <= 1}
                  className={`inline-flex items-center justify-center rounded-md border border-gray-300 bg-white text-gray-900 hover:bg-gray-50 text-sm h-9 px-3 ${currentPage <= 1 ? 'pointer-events-none opacity-50' : ''}`}
                >
                  <ChevronLeftIcon className="h-4 w-4" />
                  <span className="sr-only">Previous</span>
                </Link>
                
                {/* Page numbers */}
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  const pageNum = Math.max(1, Math.min(totalPages - 4, currentPage - 2)) + i
                  return (
                    <Link
                      key={pageNum}
                      href={`?page=${pageNum}`}
                      className={`inline-flex items-center justify-center rounded-md text-sm h-9 px-3 ${pageNum === currentPage ? 'bg-blue-600 text-white hover:bg-blue-700' : 'border border-gray-300 bg-white text-gray-900 hover:bg-gray-50'}`}
                    >
                      {pageNum}
                    </Link>
                  )
                })}

                <Link
                  href={`?page=${currentPage + 1}`}
                  aria-disabled={currentPage >= totalPages}
                  className={`inline-flex items-center justify-center rounded-md border border-gray-300 bg-white text-gray-900 hover:bg-gray-50 text-sm h-9 px-3 ${currentPage >= totalPages ? 'pointer-events-none opacity-50' : ''}`}
                >
                  <ChevronRightIcon className="h-4 w-4" />
                  <span className="sr-only">Next</span>
                </Link>
              </nav>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
