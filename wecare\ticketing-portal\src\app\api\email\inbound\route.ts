import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { writeFile, mkdir } from 'fs/promises'
import path from 'path'

export const runtime = 'nodejs'

// Helpers
function sanitizeText(input: string | undefined | null): string {
  if (!input) return ''
  // Strip HTML tags and dangerous content (very basic; consider sanitize-html later)
  return String(input)
    .replace(/<style[\s\S]*?<\/style>/gi, '')
    .replace(/<script[\s\S]*?<\/script>/gi, '')
    .replace(/<[^>]+>/g, '')
    .trim()
}

async function resolveOrganization(toEmail: string | undefined): Promise<string | null> {
  const envOrg = process.env.EMAIL_ORG_ID
  if (envOrg) return envOrg
  if (toEmail) {
    const m = /@([^>\s]+)$/.exec(toEmail)
    const domain = m?.[1]?.toLowerCase()
    if (domain) {
      const org = await prisma.organization.findFirst({ where: { domain } })
      if (org) return org.id
    }
  }
  // fallback: if only one org exists, use it
  const any = await prisma.organization.findMany({ select: { id: true }, take: 2 })
  if (any.length === 1) return any[0].id
  return null
}

async function findOrCreateCustomerUser(orgId: string, email: string) {
  const existing = await prisma.user.findUnique({ where: { email } })
  if (existing) return existing
  const fullName = email.split('@')[0]
  // minimal random password placeholder; not used for login typically
  const password = Math.random().toString(36).slice(2)
  return prisma.user.create({ data: { email, password, fullName, role: 'CUSTOMER', organizationId: orgId } })
}

async function resolveProject(orgId: string, cfg?: any): Promise<string | undefined> {
  if (cfg?.defaultProjectId) return cfg.defaultProjectId
  const envProj = process.env.EMAIL_DEFAULT_PROJECT_ID
  if (envProj) return envProj
  const p = await prisma.project.findFirst({ where: { organizationId: orgId }, select: { id: true }, orderBy: { createdAt: 'asc' } })
  return p?.id
}

async function resolveDepartment(orgId: string, toEmail: string | undefined, subject: string | undefined, cfg?: any): Promise<string | undefined> {
  // 1) Map by org settings routingMap (recipient -> department name/ID)
  try {
    const map = cfg?.routingMap as Record<string, string> | undefined
    if (map && toEmail) {
      const key = toEmail.toLowerCase()
      const val = map[key]
      if (val) {
        const byName = await prisma.department.findFirst({ where: { organizationId: orgId, name: val }, select: { id: true } })
        if (byName) return byName.id
        if (/^c[a-z0-9]{20,}$/i.test(val)) return val
      }
    }
  } catch {}
  // 2) Env JSON fallback
  try {
    const raw = process.env.EMAIL_ROUTING_MAP
    if (raw && toEmail) {
      const map = JSON.parse(raw) as Record<string, string>
      const key = toEmail.toLowerCase()
      if (map[key]) {
        const byName = await prisma.department.findFirst({ where: { organizationId: orgId, name: map[key] }, select: { id: true } })
        if (byName) return byName.id
        if (/^c[a-z0-9]{20,}$/i.test(map[key])) return map[key]
      }
    }
  } catch {}
  // 3) Subject token [DEPT:Name]
  if (subject) {
    const m = /\[(?:DEPT|DEPARTMENT):\s*([^\]]+)\]/i.exec(subject)
    const deptName = m?.[1]?.trim()
    if (deptName) {
      const dep = await prisma.department.findFirst({ where: { organizationId: orgId, name: deptName }, select: { id: true } })
      if (dep) return dep.id
    }
  }
  // 4) Default from org settings
  if (cfg?.defaultDepartmentId) return cfg.defaultDepartmentId
  // 5) Fallback: "General Support" or first department
  const general = await prisma.department.findFirst({ where: { organizationId: orgId, name: 'General Support' }, select: { id: true } })
  if (general) return general.id
  const any = await prisma.department.findFirst({ where: { organizationId: orgId }, select: { id: true }, orderBy: { createdAt: 'asc' } })
  return any?.id
}

function generateTicketNumber(): string {
  const timestamp = new Date().toISOString().replace(/[-:TZ.]/g, '').slice(0, 14)
  const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0')
  return `TKT-${timestamp}-${random}`
}

async function saveAttachmentBuffer(buffer: Buffer, originalName: string, mime: string, ticketId: string) {
  const uploadDir = path.join(process.cwd(), 'public', 'uploads')
  await mkdir(uploadDir, { recursive: true })
  const safeName = originalName.replace(/[^a-zA-Z0-9._-]/g, '_')
  const basename = `${Date.now()}-${Math.random().toString(36).slice(2,8)}-${safeName}`
  const filePath = path.join(uploadDir, basename)
  await writeFile(filePath, buffer)
  const url = `/uploads/${basename}`
  await prisma.attachment.create({
    data: {
      filename: basename,
      originalName,
      mimeType: mime || 'application/octet-stream',
      size: buffer.length,
      url,
      ticketId,
    },
  })
}

async function sendAcknowledgementEmail(opts: { to: string, ticketNumber: string }) {
  // Placeholder: implement via nodemailer or provider API after dependencies are approved
  console.log(`[ACK] Would send acknowledgment to ${opts.to} for ${opts.ticketNumber}`)
}

// Simple rate limiting per sender per hour
async function rateLimitSender(email: string, limit: number): Promise<boolean> {
  const since = new Date(Date.now() - 60 * 60 * 1000)
  const cnt = await prisma.emailThread.count({ where: { fromEmail: email, receivedAt: { gte: since } } })
  return cnt < (limit || 20)
}

export async function POST(req: NextRequest) {

  try {
    const contentType = req.headers.get('content-type') || ''

    // We support JSON payloads shaped like common inbound email webhooks
    // { subject, from, to, text, html, messageId, inReplyTo, references, attachments: [{ filename, mimeType, contentBase64 }] }
    let payload: any
    if (contentType.includes('application/json')) {
      payload = await req.json()
    } else if (contentType.includes('multipart/form-data')) {
      // Some providers send fields as form-data; only parse the basic fields here
      const form = await req.formData()
      payload = Object.fromEntries(Array.from(form.entries()))
      // attachments not handled for multipart in this minimal implementation
    } else {
      return NextResponse.json({ error: 'Unsupported content type' }, { status: 415 })
    }

    const from = String(payload.from || '').trim()
    const to = String(payload.to || '').trim()
    const subject = sanitizeText(payload.subject)
    const textBody = sanitizeText(payload.text || payload.body)
    const htmlBody = sanitizeText(payload.html)
    const body = textBody || htmlBody
    const messageId = String(payload.messageId || payload['Message-Id'] || '').trim()
    const inReplyTo = String(payload.inReplyTo || payload['In-Reply-To'] || '').trim() || undefined
    const references = Array.isArray(payload.references) ? payload.references.join(', ') : (payload.references || undefined)

    if (!from || !subject || !body) {
      return NextResponse.json({ error: 'Missing required email fields' }, { status: 400 })
    }

    // Resolve org first
    const orgId = await resolveOrganization(to)
    if (!orgId) return NextResponse.json({ error: 'Organization resolution failed' }, { status: 400 })

    // Load org settings and validate webhook secret
    const org = await prisma.organization.findUnique({ where: { id: orgId }, select: { settings: true } })
    const cfg = (org?.settings as any)?.emailConfig || {}
    const secret = req.headers.get('x-email-webhook-secret') || ''
    const expectedSecret = cfg.webhookSecret || process.env.EMAIL_WEBHOOK_SECRET || ''
    if (!expectedSecret || secret !== expectedSecret) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Rate limit with org setting
    const limit = Number(cfg.rateLimitPerHour) > 0 ? Number(cfg.rateLimitPerHour) : 20
    const allowed = await rateLimitSender(from.toLowerCase(), limit)
    if (!allowed) return NextResponse.json({ error: 'Rate limited' }, { status: 429 })

    // Duplicate detection via Message-ID
    if (messageId) {
      const exists = await prisma.emailThread.findUnique({ where: { messageId } })
      if (exists) {
        return NextResponse.json({ ok: true, deduped: true })
      }
    }

    // If reply: attach to existing ticket by inReplyTo chain
    let parentTicketId: string | undefined
    if (inReplyTo) {
      const parent = await prisma.emailThread.findUnique({ where: { messageId: inReplyTo } })
      if (parent?.ticketId) parentTicketId = parent.ticketId
    }

    // Ensure customer user
    const customer = await findOrCreateCustomerUser(orgId, from.toLowerCase())

    // Routing using org settings (fallback to env/db defaults)
    const departmentId = await resolveDepartment(orgId, to, subject, cfg)
    const projectId = await resolveProject(orgId, cfg)

    let ticketId: string
    let ticketNumber: string

    if (parentTicketId) {
      // Append as a message to existing ticket
      const t = await prisma.ticket.findUnique({ where: { id: parentTicketId }, select: { id: true, ticketNumber: true } })
      if (!t) return NextResponse.json({ error: 'Parent ticket not found' }, { status: 404 })
      ticketId = t.id
      ticketNumber = t.ticketNumber

      await prisma.ticketMessage.create({
        data: {
          content: body,
          isInternal: false,
          ticketId,
          authorId: customer.id,
        },
      })
    } else {
      ticketNumber = generateTicketNumber()
      const ticket = await prisma.ticket.create({
        data: {
          ticketNumber,
          title: subject,
          description: body,
          status: 'OPEN',
          priority: 'MEDIUM',
          organizationId: orgId,
          createdById: customer.id,
          projectId: projectId,
          departmentId: departmentId,
          metadata: messageId ? { messageId } : undefined,
        },
        select: { id: true },
      })
      ticketId = ticket.id

      await prisma.ticketMessage.create({
        data: {
          content: `[AUDIT] Ticket created from email by ${from}`,
          isInternal: true,
          ticketId,
          authorId: customer.id,
        },
      })
    }

    // Record email thread
    await prisma.emailThread.create({
      data: {
        subject: subject || '(no subject)',
        fromEmail: from,
        toEmail: to || '',
        messageId: messageId || `${Date.now()}-${Math.random().toString(36).slice(2)}`,
        inReplyTo,
        references,
        body,
        isHtml: false,
        ticketId,
        userId: customer.id,
      },
    })

    // Attachments (JSON format: attachments: [{ filename, mimeType, contentBase64 }])
    if (Array.isArray(payload.attachments) && payload.attachments.length) {
      for (const att of payload.attachments.slice(0, 5)) {
        try {
          const filename = String(att.filename || 'attachment')
          const mimeType = String(att.mimeType || 'application/octet-stream')
          const b64 = String(att.contentBase64 || '')
          if (!b64) continue
          const buffer = Buffer.from(b64, 'base64')
          await saveAttachmentBuffer(buffer, filename, mimeType, ticketId)
        } catch (e) {
          console.error('Attachment save failed', e)
        }
      }
    }

    // Send acknowledgment (placeholder)
    await sendAcknowledgementEmail({ to: from, ticketNumber })

    return NextResponse.json({ ok: true, ticketId, ticketNumber })
  } catch (e: any) {
    console.error('Email inbound error', e)
    return NextResponse.json({ error: 'Internal error' }, { status: 500 })
  }
}

