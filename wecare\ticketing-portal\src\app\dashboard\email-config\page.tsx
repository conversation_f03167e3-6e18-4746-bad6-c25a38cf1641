import { cookies } from 'next/headers'
import { redirect } from 'next/navigation'
import { verifyToken } from '@/lib/auth-new'
import { prisma } from '@/lib/prisma'
import EmailConfigForm from './EmailConfigForm'

export const dynamic = 'force-dynamic'

export default async function EmailConfigPage() {
  const cookieStore = await cookies()
  const token = cookieStore.get('auth-token')?.value
  const user = token ? verifyToken(token) : null
  if (!user) redirect('/auth/login')
  if (user.role !== 'ADMIN') redirect('/dashboard')

  const org = await prisma.organization.findUnique({ where: { id: user.organizationId }, select: { settings: true } })
  const data = { emailConfig: (org?.settings as any)?.emailConfig || {} }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Email configuration</h1>
          <p className="mt-1 text-sm text-gray-600">Manage inbound email-to-ticket automation and acknowledgements.</p>
        </div>
      </div>

      <EmailConfigForm initial={data.emailConfig || {}} />
    </div>
  )
}

