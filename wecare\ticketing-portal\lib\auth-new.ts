import { prisma } from './prisma'
import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'

export type AuthUser = {
  id: string
  email: string
  fullName?: string | null
  role: 'ADMIN' | 'MANAGER' | 'AGENT' | 'CUSTOMER'
  organizationId: string
}

const JWT_SECRET = process.env.NEXTAUTH_SECRET || 'dev-secret-change'
const JWT_EXPIRES_IN = '7d'

export async function verifyPassword(password: string, hash: string) {
  return bcrypt.compare(password, hash)
}

export function generateToken(payload: AuthUser) {
  return jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN })
}

export function verifyToken(token: string): AuthUser | null {
  try {
    return jwt.verify(token, JWT_SECRET) as AuthUser
  } catch {
    return null
  }
}

export async function signIn(email: string, password: string) {
  const user = await prisma.user.findUnique({ where: { email } })
  if (!user) return null

  const valid = await verifyPassword(password, user.password)
  if (!valid) return null

  const authUser: AuthUser = {
    id: user.id,
    email: user.email,
    fullName: user.fullName,
    role: user.role as AuthUser['role'],
    organizationId: user.organizationId,
  }

  const token = generateToken(authUser)
  return { user: authUser, token }
}

