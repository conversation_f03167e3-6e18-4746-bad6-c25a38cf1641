import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { verifyToken } from '@/lib/auth-new'

export const runtime = 'nodejs'

function isIdLike(val: string) {
  return /^c[a-z0-9]{20,}$/i.test(val)
}

export async function POST(req: NextRequest) {
  try {
    const token = req.cookies.get('auth-token')?.value
    const user = token ? verifyToken(token) : null
    if (!user) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    if (user.role !== 'ADMIN') return NextResponse.json({ error: 'Forbidden' }, { status: 403 })

    const body = await req.json().catch(() => null)
    const to: string = String(body?.to || '').trim().toLowerCase()
    const subject: string | undefined = body?.subject ? String(body.subject) : undefined
    if (!to) return NextResponse.json({ error: 'Missing to' }, { status: 400 })

    // Load org cfg
    const org = await prisma.organization.findUnique({ where: { id: user.organizationId }, select: { id: true, settings: true, name: true } })
    const cfg = (org?.settings as any)?.emailConfig || {}

    // Resolve project
    let projectId: string | undefined
    if (cfg?.defaultProjectId) {
      projectId = cfg.defaultProjectId
    } else {
      const p = await prisma.project.findFirst({ where: { organizationId: org!.id }, select: { id: true }, orderBy: { createdAt: 'asc' } })
      projectId = p?.id
    }

    // Resolve department
    let departmentId: string | undefined
    const routingMap: Record<string, string> | undefined = cfg?.routingMap
    if (routingMap && routingMap[to]) {
      const val = routingMap[to]
      if (isIdLike(val)) {
        departmentId = val
      } else {
        const dep = await prisma.department.findFirst({ where: { organizationId: org!.id, name: val }, select: { id: true } })
        if (dep) departmentId = dep.id
      }
    }

    // Subject marker [DEPT:Name]
    if (!departmentId && subject) {
      const m = /\[(?:DEPT|DEPARTMENT):\s*([^\]]+)\]/i.exec(subject)
      const deptName = m?.[1]?.trim()
      if (deptName) {
        const dep = await prisma.department.findFirst({ where: { organizationId: org!.id, name: deptName }, select: { id: true } })
        if (dep) departmentId = dep.id
      }
    }

    // Defaults
    if (!departmentId && cfg?.defaultDepartmentId) departmentId = cfg.defaultDepartmentId
    if (!departmentId) {
      const general = await prisma.department.findFirst({ where: { organizationId: org!.id, name: 'General Support' }, select: { id: true } })
      if (general) departmentId = general.id
    }
    if (!departmentId) {
      const any = await prisma.department.findFirst({ where: { organizationId: org!.id }, select: { id: true }, orderBy: { createdAt: 'asc' } })
      departmentId = any?.id
    }

    const depMeta = departmentId ? await prisma.department.findUnique({ where: { id: departmentId }, select: { id: true, name: true } }) : null
    const projMeta = projectId ? await prisma.project.findUnique({ where: { id: projectId }, select: { id: true, name: true } }) : null

    return NextResponse.json({
      ok: true,
      to,
      resolved: {
        departmentId: depMeta?.id,
        departmentName: depMeta?.name,
        projectId: projMeta?.id,
        projectName: projMeta?.name,
      }
    })
  } catch (e: any) {
    console.error('email config test error', e)
    return NextResponse.json({ error: 'Internal error' }, { status: 500 })
  }
}

