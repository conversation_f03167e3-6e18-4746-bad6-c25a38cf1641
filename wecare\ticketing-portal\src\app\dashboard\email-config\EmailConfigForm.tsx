"use client"

import { useEffect, useMemo, useState } from 'react'

type EmailConfig = {
  webhookSecret?: string
  routingMap?: Record<string, string>
  defaultProjectId?: string
  defaultDepartmentId?: string
  rateLimitPerHour?: number
  smtp?: {
    host?: string
    port?: number
    user?: string
    pass?: string
    fromEmail?: string
    fromName?: string
    secure?: boolean
  }
}

type Project = { id: string; name: string }
type Department = { id: string; name: string }

export default function EmailConfigForm({ initial }: { initial: EmailConfig }) {
  const [config, setConfig] = useState<EmailConfig>(initial || {})
  const [projects, setProjects] = useState<Project[]>([])
  const [departments, setDepartments] = useState<Department[]>([])
  const [saving, setSaving] = useState(false)
  const [message, setMessage] = useState<string | null>(null)
  // Address book rows derived from routingMap
  const [rows, setRows] = useState<Array<{ toEmail: string; departmentId: string }>>([])
  const [rowMsgs, setRowMsgs] = useState<Record<number, string>>({})

  useEffect(() => {
    async function loadRefs() {
      try {
        const [prj, dept] = await Promise.all([
          fetch('/api/projects').then(r => r.json()).catch(() => ({ projects: [] })),
          fetch('/api/departments').then(r => r.json()).catch(() => ({ departments: [] })),
        ])
        setProjects(prj.projects || [])
        setDepartments(dept.departments || [])
      } catch {}
    }
    loadRefs()
  }, [])

  useEffect(() => {
    const m = config.routingMap || {}
    const next = Object.entries(m).map(([to, dep]) => ({ toEmail: to, departmentId: String(dep || '') }))
    if (next.length === 0) next.push({ toEmail: '', departmentId: '' })
    setRows(next)
  }, [config.routingMap])

  function update<K extends keyof EmailConfig>(key: K, value: EmailConfig[K]) {
    setConfig(prev => ({ ...prev, [key]: value }))
  }

  async function onSubmit(e: React.FormEvent) {
    e.preventDefault()
    setSaving(true)
    setMessage(null)
    try {
      const routingMap: Record<string, string> = {}
      for (const r of rows) {
        const to = (r.toEmail || '').trim().toLowerCase()
        if (to && r.departmentId) routingMap[to] = r.departmentId
      }
      const payload = { ...config, routingMap }
      const res = await fetch('/api/email/config', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      })
      if (!res.ok) throw new Error('Save failed')
      setMessage('Saved successfully')
    } catch (err: any) {
      setMessage(err?.message || 'Failed to save')
    } finally {
      setSaving(false)
    }
  }

  return (
    <form onSubmit={onSubmit} className="space-y-6">
      <div className="bg-white rounded-lg shadow p-4 space-y-4">
        <h2 className="font-semibold">Webhook</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm text-gray-600">Webhook secret</label>
            <input type="text" value={config.webhookSecret || ''} onChange={e => update('webhookSecret', e.target.value)} className="mt-1 w-full border rounded px-3 py-2" placeholder="e.g., random-long-secret" />
            <p className="text-xs text-gray-500 mt-1">Header: x-email-webhook-secret must match.</p>
          </div>
          <div>
            <label className="block text-sm text-gray-600">Rate limit (per hour per sender)</label>
            <input type="number" value={config.rateLimitPerHour ?? 20} onChange={e => update('rateLimitPerHour', Number(e.target.value))} className="mt-1 w-full border rounded px-3 py-2" />
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow p-4 space-y-4">
        <h2 className="font-semibold">Routing</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm text-gray-600">Default Project</label>
            <select value={config.defaultProjectId || ''} onChange={e => update('defaultProjectId', e.target.value)} className="mt-1 w-full border rounded px-3 py-2">
              <option value="">— None —</option>
              {projects.map(p => <option key={p.id} value={p.id}>{p.name}</option>)}
            </select>
          </div>
          <div>
            <label className="block text-sm text-gray-600">Default Department</label>
            <select value={config.defaultDepartmentId || ''} onChange={e => update('defaultDepartmentId', e.target.value)} className="mt-1 w-full border rounded px-3 py-2">
              <option value="">— None —</option>
              {departments.map(d => <option key={d.id} value={d.id}>{d.name}</option>)}
            </select>
          </div>
          <div className="md:col-span-2">
            <label className="block text-sm text-gray-600 mb-2">Address book (Recipient → Department)</label>
            <div className="space-y-3">
              {rows.map((row, idx) => (
                <div key={idx} className="grid grid-cols-1 md:grid-cols-12 gap-2 items-center">
                  <div className="md:col-span-5">
                    <input
                      placeholder="<EMAIL>"
                      className="w-full border rounded px-3 py-2"
                      value={row.toEmail}
                      onChange={e => setRows(prev => prev.map((r,i) => i===idx ? { ...r, toEmail: e.target.value } : r))}
                    />
                  </div>
                  <div className="md:col-span-4">
                    <select
                      className="w-full border rounded px-3 py-2"
                      value={row.departmentId}
                      onChange={e => setRows(prev => prev.map((r,i) => i===idx ? { ...r, departmentId: e.target.value } : r))}
                    >
                      <option value="">— Select department —</option>
                      {departments.map(d => <option key={d.id} value={d.id}>{d.name}</option>)}
                    </select>
                  </div>
                  <div className="md:col-span-4 flex flex-wrap gap-2">
                    <button type="button" className="border rounded px-3 py-2 text-sm" onClick={async () => {
                      const to = (row.toEmail||'').trim()
                      if (!to) { setRowMsgs(m => ({...m, [idx]: 'Enter a recipient email first'})); return }
                      setRowMsgs(m => ({...m, [idx]: 'Testing routing...'}))
                      try {
                        const res = await fetch('/api/email/config/test', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ to }) })
                        const data = await res.json()
                        if (!res.ok) throw new Error(data?.error || 'Failed')
                        const d = data?.resolved
                        setRowMsgs(m => ({...m, [idx]: `Department: ${d?.departmentName || d?.departmentId || 'n/a'} • Project: ${d?.projectName || d?.projectId || 'n/a'}`}))
                      } catch (e:any) {
                        setRowMsgs(m => ({...m, [idx]: e?.message || 'Test failed'}))
                      }
                    }}>Test routing</button>
                    <button type="button" className="border rounded px-3 py-2 text-sm" onClick={async () => {
                      const to = (row.toEmail||'').trim()
                      if (!to) { setRowMsgs(m => ({...m, [idx]: 'Enter a recipient email first'})); return }
                      setRowMsgs(m => ({...m, [idx]: 'Sending test email...'}))
                      try {
                        const res = await fetch('/api/email/config/test/send-ack', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ to }) })
                        const data = await res.json()
                        if (!res.ok) throw new Error(data?.error || 'Failed')
                        setRowMsgs(m => ({...m, [idx]: `Test email sent ✓ (messageId: ${data?.messageId || 'n/a'})`}))
                      } catch (e:any) {
                        setRowMsgs(m => ({...m, [idx]: e?.message || 'Send failed'}))
                      }
                    }}>Send test email</button>
                    <button type="button" className="border rounded px-3 py-2 text-sm" onClick={() => setRows(prev => prev.filter((_,i) => i!==idx))}>Remove</button>
                  </div>
                  {rowMsgs[idx] && <div className="md:col-span-12 text-xs text-gray-600">{rowMsgs[idx]}</div>}
                </div>
              ))}
              <button type="button" className="inline-flex items-center justify-center rounded border border-gray-300 text-gray-700 hover:bg-gray-50 h-9 px-3 py-1" onClick={() => setRows(prev => [...prev, { toEmail: '', departmentId: '' }])}>+ Add address</button>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow p-4 space-y-4">
        <h2 className="font-semibold">Acknowledgement Email (SMTP)</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm text-gray-600">SMTP Host</label>
            <input className="mt-1 w-full border rounded px-3 py-2" value={config.smtp?.host || ''} onChange={e => setConfig(prev => ({ ...prev, smtp: { ...(prev.smtp||{}), host: e.target.value } }))} />
          </div>
          <div>
            <label className="block text-sm text-gray-600">SMTP Port</label>
            <input type="number" className="mt-1 w-full border rounded px-3 py-2" value={config.smtp?.port ?? 587} onChange={e => setConfig(prev => ({ ...prev, smtp: { ...(prev.smtp||{}), port: Number(e.target.value) } }))} />
          </div>
          <div>
            <label className="block text-sm text-gray-600">User</label>
            <input className="mt-1 w-full border rounded px-3 py-2" value={config.smtp?.user || ''} onChange={e => setConfig(prev => ({ ...prev, smtp: { ...(prev.smtp||{}), user: e.target.value } }))} />
          </div>
          <div>
            <label className="block text-sm text-gray-600">Password</label>
            <input type="password" className="mt-1 w-full border rounded px-3 py-2" value={config.smtp?.pass || ''} onChange={e => setConfig(prev => ({ ...prev, smtp: { ...(prev.smtp||{}), pass: e.target.value } }))} />
          </div>
          <div>
            <label className="block text-sm text-gray-600">From Email</label>
            <input className="mt-1 w-full border rounded px-3 py-2" value={config.smtp?.fromEmail || ''} onChange={e => setConfig(prev => ({ ...prev, smtp: { ...(prev.smtp||{}), fromEmail: e.target.value } }))} />
          </div>
          <div>
            <label className="block text-sm text-gray-600">From Name</label>
            <input className="mt-1 w-full border rounded px-3 py-2" value={config.smtp?.fromName || ''} onChange={e => setConfig(prev => ({ ...prev, smtp: { ...(prev.smtp||{}), fromName: e.target.value } }))} />
          </div>
          <div>
            <label className="inline-flex items-center text-sm text-gray-600 mt-6">
              <input type="checkbox" className="mr-2" checked={!!config.smtp?.secure} onChange={e => setConfig(prev => ({ ...prev, smtp: { ...(prev.smtp||{}), secure: e.target.checked } }))} />
              Use TLS (secure)
            </label>
          </div>
        </div>
      </div>

      <div className="flex items-center gap-3">
        <button type="submit" disabled={saving} className="inline-flex items-center justify-center rounded bg-blue-600 text-white hover:bg-blue-700 h-10 px-4 py-2">
          {saving ? 'Saving…' : 'Save configuration'}
        </button>
        {message && <span className="text-sm text-gray-600">{message}</span>}
      </div>
    </form>
  )
}

