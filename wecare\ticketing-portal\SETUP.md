# Ticketing Portal Setup Guide

## Prerequisites

1. Node.js 18+ installed
2. A Supabase account and project
3. Git (for version control)

## Supabase Setup

### 1. Create a Supabase Project

1. Go to [supabase.com](https://supabase.com) and create a new project
2. Wait for the project to be fully initialized
3. Go to Settings > API to get your project URL and anon key

### 2. Set up Environment Variables

1. Copy `.env.local.example` to `.env.local`
2. Fill in your Supabase credentials:

```bash
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

### 3. Run Database Migrations

You can run the SQL migrations in the Supabase SQL Editor:

1. Go to your Supabase dashboard
2. Navigate to SQL Editor
3. Run the migration files in order:
   - `supabase/migrations/001_initial_schema.sql`
   - `supabase/migrations/002_rls_policies.sql`
   - `supabase/migrations/003_functions.sql`
   - `supabase/migrations/004_sample_data.sql`

Alternatively, if you have the Supabase CLI installed:

```bash
# Install Supabase CLI
npm install -g supabase

# Login to Supabase
supabase login

# Link your project
supabase link --project-ref your-project-ref

# Run migrations
supabase db push
```

### 4. Configure Authentication

1. In your Supabase dashboard, go to Authentication > Settings
2. Configure your site URL: `http://localhost:3000`
3. Add any additional redirect URLs you need
4. Enable email authentication or configure other providers as needed

### 5. Set up Storage (Optional)

If you want file upload functionality:

1. Go to Storage in your Supabase dashboard
2. Create a bucket named `ticket-attachments`
3. Set up appropriate policies for file access

## Local Development

### 1. Install Dependencies

```bash
npm install
```

### 2. Run the Development Server

```bash
npm run dev
```

The application will be available at `http://localhost:3000`

## Project Structure

```
ticketing-portal/
├── src/
│   ├── app/                 # Next.js app router pages
│   ├── components/          # React components
│   └── lib/                 # Utility functions and configurations
├── components/              # Shared UI components
├── lib/                     # Core utilities and Supabase client
├── supabase/               # Database migrations and types
└── public/                 # Static assets
```

## Key Features

- **Multi-tenant Architecture**: Organizations with role-based access
- **Ticket Management**: Create, assign, and track tickets
- **Real-time Updates**: Live updates using Supabase realtime
- **Email Integration**: Convert emails to tickets (requires SMTP setup)
- **File Attachments**: Upload and manage ticket attachments
- **Role-based Access**: Admin, Agent, and Customer roles
- **Responsive Design**: Works on desktop and mobile devices

## User Roles

- **Admin**: Full access to organization settings and all tickets
- **Agent**: Can view and manage tickets assigned to them or unassigned
- **Customer**: Can create tickets and view their own tickets

## Next Steps

1. Set up your Supabase project and run the migrations
2. Configure your environment variables
3. Start the development server
4. Create your first organization and user accounts
5. Test the ticket creation and management workflow

## Email Integration (Optional)

To enable email-to-ticket functionality:

1. Set up SMTP credentials in your environment variables
2. Configure email forwarding to your application
3. Implement email parsing webhook (additional development required)

## Production Deployment

For production deployment:

1. Set up your production Supabase project
2. Configure environment variables for production
3. Deploy to Vercel, Netlify, or your preferred hosting platform
4. Set up proper domain and SSL certificates
5. Configure email integration for production use
