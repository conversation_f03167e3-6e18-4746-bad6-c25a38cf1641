import { cookies } from 'next/headers'
import { prisma } from '@/lib/prisma'
import { verifyToken } from '@/lib/auth-new'
import { getUserPermissions, hasPermission } from '@/lib/permissions'

function fmt(d: Date) {
  return d.toISOString().slice(0, 10)
}

export default async function ReportsPage({ searchParams }: { searchParams?: Record<string, string | string[] | undefined> }) {
  const cookieStore = await cookies()
  const token = cookieStore.get('auth-token')?.value
  const user = token ? verifyToken(token) : null
  if (!user) return <div className="p-6">Unauthorized</div>

  // Permission: non-admins must have TICKETS_VIEW to see reports
  if (user.role !== 'ADMIN') {
    const perms = await getUserPermissions({ id: user.id, role: user.role, organizationId: user.organizationId })
    if (!hasPermission(perms, 'TICKETS_VIEW')) {
      return <div className="p-6">Forbidden</div>
    }
  }

  // Scope by projects and departments for non-admins
  const memberships = await prisma.userProject.findMany({ where: { userId: user.id }, select: { projectId: true } })
  const projectIds = memberships.map(m => m.projectId)
  const deptLinks = await prisma.userDepartment.findMany({ where: { userId: user.id }, select: { departmentId: true } })
  const departmentIds = deptLinks.map(d => d.departmentId)

  const baseWhere: any = user.role === 'ADMIN' ? { organizationId: user.organizationId } : {
    organizationId: user.organizationId,
    projectId: { in: projectIds.length ? projectIds : ['__none__'] },
    departmentId: { in: departmentIds.length ? departmentIds : ['__none__'] },
  }

  // Filters
  const status = typeof searchParams?.status === 'string' ? searchParams!.status.trim() : ''
  const projectId = typeof searchParams?.projectId === 'string' ? searchParams!.projectId.trim() : ''
  const departmentId = typeof searchParams?.departmentId === 'string' ? searchParams!.departmentId.trim() : ''
  const fromStr = typeof searchParams?.from === 'string' ? searchParams!.from.trim() : ''
  const toStr = typeof searchParams?.to === 'string' ? searchParams!.to.trim() : ''

  const where: any = {
    ...baseWhere,
    ...(status ? { status } : {}),
    ...(projectId ? { projectId } : {}),
    ...(departmentId ? { departmentId } : {}),
    ...(fromStr ? { createdAt: { gte: new Date(fromStr) } } : {}),
    ...(toStr ? { createdAt: { ...(fromStr ? { gte: new Date(fromStr) } : {}), lte: new Date(toStr) } } : {}),
  }

  // For select options
  const [projectsList, departmentsList] = await Promise.all([
    user.role === 'ADMIN'
      ? prisma.project.findMany({ where: { organizationId: user.organizationId }, select: { id: true, name: true } })
      : prisma.project.findMany({ where: { id: { in: projectIds.length ? projectIds : ['__none__'] } }, select: { id: true, name: true } }),
    user.role === 'ADMIN'
      ? prisma.department.findMany({ where: { organizationId: user.organizationId }, select: { id: true, name: true } })
      : prisma.department.findMany({ where: { id: { in: departmentIds.length ? departmentIds : ['__none__'] } }, select: { id: true, name: true } }),
  ])

  // Stats
  const [countsByStatus, countsByDeptRaw, countsByProjRaw, totals, timeWindowTickets] = await Promise.all([
    prisma.ticket.groupBy({ by: ['status'], where, _count: { _all: true } }),
    prisma.ticket.groupBy({ by: ['departmentId'], where, _count: { _all: true } }),
    prisma.ticket.groupBy({ by: ['projectId'], where, _count: { _all: true } }),
    Promise.all([
      prisma.ticket.count({ where }),
      prisma.ticket.count({ where: { ...where, status: 'OPEN' } }),
      prisma.ticket.count({ where: { ...where, status: 'IN_PROGRESS' } }),
      prisma.ticket.count({ where: { ...where, status: 'RESOLVED' } }),
      prisma.ticket.count({ where: { ...where, status: 'CLOSED' } }),
      prisma.ticket.count({ where: { ...where, status: 'CANCELLED' } }),
    ]),
    // time series (last 30 days or provided window)
    prisma.ticket.findMany({
      where: {
        ...where,
        ...(fromStr || toStr
          ? {}
          : { createdAt: { gte: new Date(Date.now() - 29 * 24 * 3600 * 1000) } }),
      },
      select: { id: true, createdAt: true },
      orderBy: { createdAt: 'asc' },
    }),
  ])

  const [total, open, wip, resolved, closed, cancelled] = totals

  // Map dept/project names
  const deptNameMap = new Map(departmentsList.map(d => [d.id, d.name]))
  const projNameMap = new Map(projectsList.map(p => [p.id, p.name]))

  // Build timeseries counts by day
  const seriesMap = new Map<string, number>()
  for (const t of timeWindowTickets) {
    const day = fmt(t.createdAt)
    seriesMap.set(day, (seriesMap.get(day) || 0) + 1)
  }
  // Ensure continuous window for nice output (last 14 days for brevity)
  const now = new Date()
  const start = new Date(fromStr || new Date(Date.now() - 13 * 24 * 3600 * 1000))
  const series: { day: string; count: number }[] = []
  for (let d = new Date(start); d <= now; d = new Date(d.getTime() + 24 * 3600 * 1000)) {
    const key = fmt(d)
    series.push({ day: key, count: seriesMap.get(key) || 0 })
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Reports</h1>
              <p className="mt-1 text-sm text-gray-600">Organization analytics (scoped to your access)</p>
            </div>
          </div>
        </div>

        {/* Filters */}
        <form method="GET" className="bg-white rounded-lg shadow p-4">
          <div className="grid grid-cols-1 md:grid-cols-6 gap-3">
            <div>
              <label className="block text-xs text-gray-600">Status</label>
              <select name="status" defaultValue={status} className="mt-1 w-full border rounded px-3 py-2">
                <option value="">All</option>
                {['OPEN','IN_PROGRESS','WAITING_FOR_CUSTOMER','WAITING_FOR_AGENT','RESOLVED','CLOSED','CANCELLED'].map(s => (
                  <option key={s} value={s}>{s.replaceAll('_',' ')}</option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-xs text-gray-600">Project</label>
              <select name="projectId" defaultValue={projectId} className="mt-1 w-full border rounded px-3 py-2">
                <option value="">All</option>
                {projectsList.map((p:any) => (
                  <option key={p.id} value={p.id}>{p.name}</option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-xs text-gray-600">Department</label>
              <select name="departmentId" defaultValue={departmentId} className="mt-1 w-full border rounded px-3 py-2">
                <option value="">All</option>
                {departmentsList.map((d:any) => (
                  <option key={d.id} value={d.id}>{d.name}</option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-xs text-gray-600">From</label>
              <input type="date" name="from" defaultValue={fromStr} className="mt-1 w-full border rounded px-3 py-2" />
            </div>
            <div>
              <label className="block text-xs text-gray-600">To</label>
              <input type="date" name="to" defaultValue={toStr} className="mt-1 w-full border rounded px-3 py-2" />
            </div>
            <div className="flex items-end gap-2">
              <button type="submit" className="inline-flex items-center justify-center rounded bg-blue-600 text-white hover:bg-blue-700 h-10 px-4 py-2">Apply</button>
              <a href="/dashboard/reports" className="inline-flex items-center justify-center rounded border border-gray-300 text-gray-700 hover:bg-gray-50 h-10 px-4 py-2">Reset</a>
            </div>
          </div>
        </form>

        {/* KPI cards */}
        <div className="grid grid-cols-2 md:grid-cols-6 gap-4">
          {[{label:'Open',value:open},{label:'WIP',value:wip},{label:'Resolved',value:resolved},{label:'Closed',value:closed},{label:'Discarded',value:cancelled},{label:'Total',value:total}].map(s=> (
            <div key={s.label} className="bg-white rounded-lg shadow p-4">
              <div className="text-sm text-gray-500">{s.label}</div>
              <div className="text-2xl font-bold">{s.value}</div>
            </div>
          ))}
        </div>

        {/* By status */}
        <div className="bg-white rounded-lg shadow">
          <div className="p-4 border-b"><h2 className="font-semibold">Tickets by Status</h2></div>
          <ul className="divide-y">
            {countsByStatus.sort((a,b)=>a.status.localeCompare(b.status)).map(row => (
              <li key={row.status} className="p-3 flex items-center justify-between">
                <div className="text-sm">{row.status.replaceAll('_',' ')}</div>
                <div className="text-sm font-semibold">{row._count._all}</div>
              </li>
            ))}
            {countsByStatus.length === 0 && <li className="p-4 text-sm text-gray-500">No data.</li>}
          </ul>
        </div>

        {/* By department */}
        <div className="bg-white rounded-lg shadow">
          <div className="p-4 border-b"><h2 className="font-semibold">Tickets by Department</h2></div>
          <ul className="divide-y">
            {countsByDeptRaw.sort((a,b)=> (deptNameMap.get(a.departmentId||'')||'').localeCompare(deptNameMap.get(b.departmentId||'')||'')).map(row => (
              <li key={row.departmentId || 'none'} className="p-3 flex items-center justify-between">
                <div className="text-sm">{deptNameMap.get(row.departmentId || '') || 'Unassigned'}</div>
                <div className="text-sm font-semibold">{row._count._all}</div>
              </li>
            ))}
            {countsByDeptRaw.length === 0 && <li className="p-4 text-sm text-gray-500">No data.</li>}
          </ul>
        </div>

        {/* By project */}
        <div className="bg-white rounded-lg shadow">
          <div className="p-4 border-b"><h2 className="font-semibold">Tickets by Project</h2></div>
          <ul className="divide-y">
            {countsByProjRaw.sort((a,b)=> (projNameMap.get(a.projectId||'')||'').localeCompare(projNameMap.get(b.projectId||'')||'')).map(row => (
              <li key={row.projectId || 'none'} className="p-3 flex items-center justify-between">
                <div className="text-sm">{projNameMap.get(row.projectId || '') || 'Unassigned'}</div>
                <div className="text-sm font-semibold">{row._count._all}</div>
              </li>
            ))}
            {countsByProjRaw.length === 0 && <li className="p-4 text-sm text-gray-500">No data.</li>}
          </ul>
        </div>

        {/* Created over time */}
        <div className="bg-white rounded-lg shadow">
          <div className="p-4 border-b"><h2 className="font-semibold">Tickets Created Over Time</h2></div>
          <div className="p-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-xs text-gray-700">
              {series.slice(-14).map(pt => (
                <div key={pt.day} className="flex items-center justify-between bg-gray-50 rounded px-3 py-2">
                  <span>{pt.day}</span>
                  <span className="font-semibold">{pt.count}</span>
                </div>
              ))}
              {series.length === 0 && (
                <div className="text-sm text-gray-500">No tickets in this period.</div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

